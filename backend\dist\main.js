"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const platform_fastify_1 = require("@nestjs/platform-fastify");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const config_service_1 = require("./config/config.service");
async function bootstrap() {
    const isDevOrTest = process.env.NODE_ENV === "development" || process.env.NODE_ENV === "test";
    const app = await core_1.NestFactory.create(app_module_1.AppModule, new platform_fastify_1.FastifyAdapter({ logger: true }));
    const configService = app.get(config_service_1.ConfigService);
    const localDomains = configService.getLocalDomains();
    const allowedOrigins = isDevOrTest && !configService.shouldBlockLocalhost()
        ? [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:3002',
            'http://localhost:3003',
            'http://localhost:3004',
            'http://localhost:3005',
            'http://localhost:3060',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://127.0.0.1:3002',
            'http://127.0.0.1:3003',
            'http://127.0.0.1:3004',
            'http://127.0.0.1:3005',
            'http://127.0.0.1:3060',
            localDomains.backend,
            localDomains.frontend,
            localDomains.admin,
            ...configService.getAllowedOrigins()
        ]
        : configService.getAllowedOrigins();
    await app.register(require("@fastify/cors"), {
        origin: allowedOrigins,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
    });
    await app.register(require('@fastify/cookie'), {
        secret: process.env.COOKIE_SECRET || 'your-secret-key-here',
    });
    await app.register(require('@fastify/multipart'), {
        limits: {
            fileSize: 10 * 1024 * 1024,
        },
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
    }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Customer Microservice API')
        .setDescription('Customer Management API Documentation')
        .setVersion('1.0')
        .addTag('customers-query', 'Customer search, filtering, and read operations')
        .addTag('customers-management', 'Customer create, update, and delete operations')
        .addTag('customers-verification', 'Customer email, phone, and KYC verification')
        .addTag('customers-admin', 'Administrative customer operations')
        .addTag('Authentication', 'Authentication and user management')
        .addTag('transactions', 'Transaction processing and OTP verification')
        .addTag('health', 'Health check endpoints')
        .addTag('webhooks', 'Generic webhook endpoints')
        .addBearerAuth()
        .addCookieAuth('access_token', {
        type: 'apiKey',
        in: 'cookie',
        name: 'access_token',
        description: 'Access token stored in cookie'
    })
        .addCookieAuth('refresh_token', {
        type: 'apiKey',
        in: 'cookie',
        name: 'refresh_token',
        description: 'Refresh token stored in cookie'
    })
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup(`/api`, app, document, {
        swaggerOptions: {
            persistAuthorization: true,
        },
    });
    const port = configService.getNumber("PORT", 3001);
    const listeningPort = process.env.LISTENING_PORT ? parseInt(process.env.LISTENING_PORT, 10) : 0;
    const bindAddress = isDevOrTest ? process.env.SOURCE_IP || "127.0.0.1" : "0.0.0.0";
    await app.listen(listeningPort || port, bindAddress);
    const baseUrl = `http://localhost:${listeningPort || port}`;
    const apiDocsUrl = `${baseUrl}/api`;
    const graphqlUrl = `${baseUrl}/graphql`;
    console.log(`Application is running on: ${baseUrl}`);
    console.log(`Swagger documentation available at: ${apiDocsUrl}`);
    console.log(`GraphQL playground available at: ${graphqlUrl}`);
}
void bootstrap();
//# sourceMappingURL=main.js.map