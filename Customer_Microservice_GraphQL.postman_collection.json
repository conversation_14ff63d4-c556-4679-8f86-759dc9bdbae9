{"info": {"_postman_id": "customer-microservice-graphql", "name": "Customer Microservice - GraphQL API", "description": "Complete GraphQL API collection for Customer Microservice with all queries and mutations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "customerApi", "value": "http://ng-customer-local.dev.dev1.ngnair.com:3060", "type": "string"}, {"key": "customerGraphql", "value": "http://ng-customer-local.dev.dev1.ngnair.com:3060/graphql", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}], "item": [{"name": "Authentication Queries", "item": [{"name": "Auth Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Auth status returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.authStatus.status).to.eql('ok');", "    pm.expect(jsonData.data.authStatus.service).to.eql('auth-shared');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetAuthStatus {\\n  authStatus {\\n    status\\n    service\\n    timestamp\\n    version\\n  }\\n}\"\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Get Current User (Cookies)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User data returned', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data.meCookies) {", "        pm.expect(jsonData.data.meCookies.id).to.be.a('string');", "        pm.expect(jsonData.data.meCookies.email).to.be.a('string');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCurrentUser {\\n  meCookies {\\n    id\\n    email\\n    username\\n    firstName\\n    lastName\\n    role\\n    permissions\\n    createdAt\\n    updatedAt\\n  }\\n}\"\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}]}, {"name": "Authentication Mutations", "item": [{"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Refresh successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.refreshAuth).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation RefreshAuth {\\n  refreshAuth\\n}\"\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Logout", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Logout successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.logout).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation Logout {\\n  logout\\n}\"\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}]}, {"name": "Customer Queries", "item": [{"name": "Get All Customers", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customers data returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.customers.data).to.be.an('array');", "    pm.expect(jsonData.data.customers.total).to.be.a('number');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCustomers($filter: String, $limit: Int, $page: Int) {\\n  customers(filter: $filter, limit: $limit, page: $page) {\\n    data {\\n      id\\n      nameOnCard\\n      email\\n      phoneNumber\\n      billingAddress\\n      country\\n      state\\n      city\\n      zipCode\\n      status\\n      isEmailVerified\\n      isPhoneVerified\\n      isKycVerified\\n      createdAt\\n      updatedAt\\n    }\\n    total\\n    page\\n    limit\\n  }\\n}\",\n  \"variables\": {\n    \"limit\": 10,\n    \"page\": 1\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Get Customer by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer data returned or null', function () {", "    const jsonData = pm.response.json();", "    // Customer might be null if not found", "    if (jsonData.data.customer) {", "        pm.expect(jsonData.data.customer.id).to.be.a('string');", "        pm.expect(jsonData.data.customer.email).to.be.a('string');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCustomer($id: ID!) {\\n  customer(id: $id) {\\n    id\\n    nameOnCard\\n    email\\n    phoneNumber\\n    billingAddress\\n    country\\n    state\\n    city\\n    zipCode\\n    status\\n    isEmailVerified\\n    isPhoneVerified\\n    isKycVerified\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"id\": \"{{customer_id}}\"\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Get Customer by Email", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer data returned or null', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data.customerByEmail) {", "        pm.expect(jsonData.data.customerByEmail.email).to.eql('<EMAIL>');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCustomerByEmail($email: String!) {\\n  customerByEmail(email: $email) {\\n    id\\n    nameOnCard\\n    email\\n    phoneNumber\\n    billingAddress\\n    country\\n    state\\n    city\\n    zipCode\\n    status\\n    isEmailVerified\\n    isPhoneVerified\\n    isKycVerified\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"email\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Search Customers", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Search results returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.searchCustomers).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query SearchCustomers($filter: CustomerFilterInput, $pagination: PaginationInput) {\\n  searchCustomers(filter: $filter, pagination: $pagination) {\\n    id\\n    nameOnCard\\n    email\\n    phoneNumber\\n    billingAddress\\n    country\\n    state\\n    city\\n    zipCode\\n    status\\n    isEmailVerified\\n    isPhoneVerified\\n    isKycVerified\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"filter\": {\n      \"status\": \"ACTIVE\"\n    },\n    \"pagination\": {\n      \"limit\": 10,\n      \"offset\": 0\n    }\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Customers Admin", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Admin customers data returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.customersAdmin).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"query GetCustomersAdmin($filter: CustomerFilterInput, $pagination: PaginationInput) {\\n  customersAdmin(filter: $filter, pagination: $pagination) {\\n    id\\n    nameOnCard\\n    email\\n    phoneNumber\\n    billingAddress\\n    country\\n    state\\n    city\\n    zipCode\\n    status\\n    isEmailVerified\\n    isPhoneVerified\\n    isKycVerified\\n    tags\\n    notes\\n    externalId\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"pagination\": {\n      \"limit\": 10,\n      \"offset\": 0\n    }\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}]}, {"name": "Customer Mutations", "item": [{"name": "Create Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.createCustomer.id).to.be.a('string');", "    pm.expect(jsonData.data.createCustomer.email).to.eql('<EMAIL>');", "    pm.globals.set('customer_id', jsonData.data.createCustomer.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation CreateCustomer($input: CreateCustomerInput!) {\\n  createCustomer(input: $input) {\\n    id\\n    firstName\\n    lastName\\n    email\\n    phone\\n    type\\n    status\\n    isEmailVerified\\n    isPhoneVerified\\n    isKycVerified\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"input\": {\n      \"firstName\": \"<PERSON>\",\n      \"lastName\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"type\": \"INDIVIDUAL\",\n      \"phone\": \"+1234567890\"\n    }\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Update Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.updateCustomer.id).to.be.a('string');", "    pm.expect(jsonData.data.updateCustomer.city).to.eql('Los Angeles');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation UpdateCustomer($input: UpdateCustomerInput!) {\\n  updateCustomer(input: $input) {\\n    id\\n    nameOnCard\\n    email\\n    phoneNumber\\n    billingAddress\\n    country\\n    state\\n    city\\n    zipCode\\n    status\\n    isEmailVerified\\n    isPhoneVerified\\n    isKycVerified\\n    createdAt\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"input\": {\n      \"id\": \"{{customer_id}}\",\n      \"city\": \"Los Angeles\",\n      \"state\": \"CA\"\n    }\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Delete Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer deleted successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.deleteCustomer).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation DeleteCustomer($id: ID!) {\\n  deleteCustomer(id: $id)\\n}\",\n  \"variables\": {\n    \"id\": \"{{customer_id}}\"\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}]}, {"name": "Customer Verification Mutations", "item": [{"name": "Verify Customer Email", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email verified successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.verifyCustomerEmail.isEmailVerified).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation VerifyCustomerEmail($id: ID!) {\\n  verifyCustomerEmail(id: $id) {\\n    id\\n    email\\n    isEmailVerified\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"id\": \"{{customer_id}}\"\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Verify Customer Phone", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Phone verified successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.verifyCustomerPhone.isPhoneVerified).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation VerifyCustomerPhone($id: ID!) {\\n  verifyCustomerPhone(id: $id) {\\n    id\\n    phoneNumber\\n    isPhoneVerified\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"id\": \"{{customer_id}}\"\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}, {"name": "Update Customer Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer status updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.updateCustomerStatus.status).to.eql('ACTIVE');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation UpdateCustomerStatus($id: ID!, $status: String!) {\\n  updateCustomerStatus(id: $id, status: $status) {\\n    id\\n    status\\n    updatedAt\\n  }\\n}\",\n  \"variables\": {\n    \"id\": \"{{customer_id}}\",\n    \"status\": \"ACTIVE\"\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}]}, {"name": "Transaction Mutations", "item": [{"name": "Initiate Transaction", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Transaction initiated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.initiateTransaction.transactionId).to.be.a('string');", "    pm.expect(jsonData.data.initiateTransaction.status).to.be.a('string');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"mutation InitiateTransaction($input: TransactionInput!) {\\n  initiateTransaction(input: $input) {\\n    transactionId\\n    status\\n    message\\n    userDetails {\\n      id\\n      email\\n      firstName\\n      lastName\\n      phone {\\n        masked\\n        number\\n      }\\n    }\\n    paymentDetails\\n  }\\n}\",\n  \"variables\": {\n    \"input\": {\n      \"amount\": 100.00,\n      \"currency\": \"USD\",\n      \"description\": \"Test transaction\",\n      \"customerId\": \"{{customer_id}}\"\n    }\n  }\n}"}, "url": {"raw": "{{customerGraphql}}", "host": ["{{customerGraphql}}"]}}}]}]}