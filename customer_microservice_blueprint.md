# NGnair Customer Microservice - Complete Documentation

## What is the Customer Microservice?

### **🎯 Purpose & Business Value**

The Customer Microservice is a comprehensive customer data management system designed for financial services and payment processing platforms. It serves as the central hub for all customer-related operations, providing secure storage, management, and processing of customer information while integrating seamlessly with payment providers and authentication systems.

**Core Business Functions**:
- **Customer Lifecycle Management**: Complete customer onboarding, verification, and lifecycle management
- **Payment Integration**: Secure storage and management of payment tokens from multiple providers (Elavon, TSEP)
- **Compliance & Verification**: Email, phone, and KYC verification workflows for regulatory compliance
- **Data Security**: Enterprise-grade security for sensitive customer and payment data
- **Multi-Channel Access**: Both REST API and GraphQL interfaces for different integration needs
- **Real-time Processing**: Webhook-based real-time payment and transaction processing

### **🏢 Target Use Cases**

**Financial Services**:
- Payment processing platforms requiring customer data management
- Fintech applications needing secure customer onboarding
- Banking systems requiring KYC and compliance workflows
- E-commerce platforms with payment token management needs

**Integration Scenarios**:
- **Frontend Applications**: Customer admin panels, embedded customer forms
- **Payment Providers**: Webhook integration with Elavon, TSEP, and other payment processors
- **Authentication Systems**: Integration with external JWT-based auth services
- **Business Intelligence**: Customer analytics and reporting systems

### **🔧 Key Features & Capabilities**

#### **Customer Management**
- **Complete CRUD Operations**: Create, read, update, and delete customer records
- **Advanced Search & Filtering**: Multi-criteria search with pagination and sorting
- **Customer Segmentation**: Flexible customer grouping and tagging system
- **Bulk Operations**: Batch processing for large-scale customer operations
- **Data Export**: Compliance-ready data export functionality

#### **Verification & Compliance**
- **Email Verification**: Automated email verification workflows
- **Phone Verification**: SMS-based phone number verification
- **KYC Processing**: Know Your Customer verification and documentation
- **Status Management**: Comprehensive customer status tracking (Active, Suspended, Blocked, etc.)
- **Audit Trail**: Complete audit logging for compliance and security

#### **Payment Integration**
- **Multi-Provider Support**: Integration with Elavon, TSEP, and extensible for other providers
- **Secure Token Storage**: Encrypted storage of payment tokens and sensitive data
- **Webhook Processing**: Real-time webhook processing for payment events
- **Transaction Tracking**: Complete transaction history and status tracking
- **OTP Integration**: One-time password support for secure transactions

#### **Security & Authentication**
- **JWT Integration**: Seamless integration with external authentication services
- **Role-Based Access Control**: Granular permissions for different user types
- **Cookie-Based Authentication**: Secure cookie handling for web applications
- **Encryption**: AES-256-GCM encryption for sensitive data
- **Rate Limiting**: API protection against abuse and attacks

#### **API Interfaces**
- **GraphQL API**: Modern, flexible API with real-time subscriptions
- **REST API**: Traditional REST endpoints for legacy system integration
- **Swagger Documentation**: Complete API documentation with interactive testing
- **Real-time Updates**: WebSocket-based real-time data synchronization

#### **Administrative Features**
- **Admin Panel Integration**: Ready-to-use admin interface components
- **Customer Support Tools**: Tools for customer service representatives
- **Reporting & Analytics**: Built-in reporting capabilities
- **System Monitoring**: Health checks and performance monitoring

### **📊 Technical Specifications**

**Performance**:
- **High Throughput**: Designed to handle thousands of concurrent requests
- **Low Latency**: Optimized database queries and caching strategies
- **Scalability**: Horizontal scaling support with stateless architecture
- **Reliability**: 99.9% uptime with comprehensive error handling

**Data Management**:
- **PostgreSQL Database**: Enterprise-grade relational database
- **Prisma ORM**: Type-safe database access with migration support
- **Soft Deletes**: Data retention for audit and compliance
- **Backup & Recovery**: Automated backup and disaster recovery procedures

**Integration Capabilities**:
- **Webhook Support**: Bi-directional webhook integration
- **Event Streaming**: Real-time event publishing for system integration
- **API Versioning**: Backward-compatible API versioning strategy
- **Multi-tenant Support**: Configurable for multi-tenant deployments

### **🔗 API Endpoints Documentation**

#### **REST API Endpoints**

**Health & Monitoring**:
```
GET /health                    - Public health check
GET /health/admin             - Admin health check (requires auth)
```

**Authentication**:
```
GET /auth/status              - Auth service status (public)
GET /auth/me/cookies          - Get current user from cookies
POST /auth/refresh            - Refresh authentication tokens
POST /auth/logout             - Logout and clear cookies
```

**Customer Query Operations**:
```
GET /customers                - List all customers with filtering/pagination
GET /customers/:id            - Get specific customer by ID
GET /customers/email/:email   - Find customer by email address
```

**Customer Management Operations**:
```
POST /customers               - Create new customer
PUT /customers/:id            - Update existing customer
DELETE /customers/:id         - Delete customer (soft delete)
```

**Customer Verification Operations**:
```
POST /customers/:id/verify-email  - Verify customer email
POST /customers/:id/verify-phone  - Verify customer phone
POST /customers/:id/verify-kyc    - Verify customer KYC
```

**Customer Admin Operations** (Admin only):
```
POST /customers/:id/suspend       - Suspend customer account
POST /customers/:id/activate      - Activate customer account
POST /customers/:id/block         - Block customer account
PUT /customers/:id/admin-update   - Admin update customer data
```

**Transaction Operations**:
```
POST /transactions/initiate   - Initiate transaction with OTP
```

**Webhook Operations**:
```
POST /webhooks                - Generic webhook handler
POST /webhooks/elavon         - Elavon payment webhook
POST /webhooks/tsep           - TSEP payment webhook
GET /webhooks/health          - Webhook service health check
```

#### **GraphQL API Operations**

**Queries**:
```graphql
# Authentication
authStatus: AuthStatus!                    # Get auth service status
meCookies: User!                          # Get current user from cookies

# Customer Queries
customers(filter: String, limit: Int, page: Int): PaginatedCustomers!
customer(id: ID!): Customer
customerByEmail(email: String!): Customer
searchCustomers(filter: CustomerFilterInput, pagination: PaginationInput): [Customer!]!
customersAdmin(filter: CustomerFilterInput, pagination: PaginationInput): [Customer!]!
```

**Mutations**:
```graphql
# Authentication
refreshAuth: Boolean!                     # Refresh authentication tokens
logout: Boolean!                          # Logout user

# Customer Management
createCustomer(input: CreateCustomerInput!): Customer!
updateCustomer(input: UpdateCustomerInput!): Customer!
deleteCustomer(id: ID!): Boolean!

# Customer Verification
verifyCustomerEmail(id: ID!): Customer!
verifyCustomerPhone(id: ID!): Customer!

# Admin Operations
updateCustomerStatus(id: ID!, status: String!): Customer!

# Transactions
initiateTransaction(input: TransactionInput!): TransactionResponseType!

# Testing/Debug (Development only)
decodeJwt(input: DecodeJwtInput!): DecodeJwtResponse!
decryptToken(input: DecryptTokenInput!): DecryptTokenResponse!
```

#### **Customer Schema Structure**

**Core Customer Fields**:
```typescript
{
  id: string                    // UUID primary key
  nameOnCard: string           // Full name as on payment card
  email: string                // Email address (unique)
  phoneNumber: string          // Phone number with country code
  billingAddress: string       // Billing address
  country: string              // Country code (ISO)
  state: string                // State/province
  city: string                 // City
  zipCode: string              // Postal/ZIP code

  // Status & Verification
  status: CustomerStatus       // ACTIVE, INACTIVE, SUSPENDED, BLOCKED, etc.
  isEmailVerified: boolean     // Email verification status
  isPhoneVerified: boolean     // Phone verification status
  isKycVerified: boolean       // KYC verification status

  // Optional Fields
  dateOfBirth?: Date           // Date of birth
  externalId?: string          // External system reference
  tags?: string[]              // Customer tags for segmentation
  notes?: string               // Admin notes

  // Audit Fields
  createdAt: DateTime          // Creation timestamp
  updatedAt: DateTime          // Last update timestamp
  deletedAt?: DateTime         // Soft delete timestamp
}
```

**Authentication & Authorization**:
- **JWT Tokens**: Bearer token authentication for REST API
- **Cookie Authentication**: Encrypted cookie-based auth for web apps
- **Role-Based Access**: Admin, User, and custom role support
- **Permission System**: Granular permissions for different operations

---

## How the Customer Microservice is Built

### **🏗️ Architecture Overview**

**Technology Stack**:
- **Backend Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **API Layer**: GraphQL with Apollo Server
- **Authentication**: JWT tokens with external auth service integration
- **Containerization**: Docker with multi-stage builds
- **Process Management**: PM2 for production deployment

**Project Structure**:
```
backend/
├── src/
│   ├── modules/           # Feature modules
│   │   ├── customers-management/    # Core customer CRUD operations
│   │   ├── customers-query/         # Customer search and filtering
│   │   ├── customers-admin/         # Admin-specific operations
│   │   ├── customers-verification/  # Email/phone/KYC verification
│   │   ├── customers-audit/         # Audit logging
│   │   ├── customers-access-control/ # Authorization
│   │   ├── payment-tokens/          # Payment token management
│   │   ├── transactions/            # Transaction processing
│   │   ├── graphql/                 # GraphQL schema and resolvers
│   │   └── shared/                  # Shared utilities
│   ├── prisma/            # Database schema and migrations
│   ├── webhook/           # Webhook processing
│   └── config/            # Configuration management
├── prisma/
│   ├── schema.prisma      # Database schema
│   └── migrations/        # Database migrations
└── docker-compose.yml     # Local development setup
```

### **🗄️ Database Design**

**Core Tables**:
- **customers**: Main customer entity with personal/business information
- **addresses**: Customer addresses with type classification
- **contacts**: Emergency and business contacts
- **customer_preferences**: Communication and privacy preferences
- **payment_tokens**: Secure payment method token storage
- **audit_logs**: Complete audit trail of all changes
- **customer_segments**: Customer grouping and segmentation
- **customer_segment_members**: Many-to-many relationship for segments

**Key Database Features**:
- **Indexing Strategy**: Optimized indexes for common query patterns
- **Soft Deletes**: Maintains data integrity with soft delete patterns
- **JSON Storage**: Flexible metadata storage for provider-specific data
- **Constraints**: Database-level constraints for data integrity
- **Migrations**: Version-controlled database schema changes

### **🔌 API Design**

**GraphQL Schema**:
- **Queries**: Customer search, filtering, and retrieval operations
- **Mutations**: Customer creation, updates, and verification operations
- **Types**: Strongly typed schema with comprehensive field definitions
- **Resolvers**: Efficient data fetching with N+1 query prevention
- **Subscriptions**: Real-time updates for customer data changes

**REST Endpoints**:
- **Webhook Endpoints**: `/webhook/*` for payment provider integrations
- **Health Checks**: `/health` for service monitoring
- **Metrics**: `/metrics` for performance monitoring

### **🔐 Security Implementation**

**Authentication Flow**:
1. External auth service validates JWT tokens
2. Token verification via JWKS endpoint
3. User permissions extracted from token claims
4. Role-based access control applied to operations

**Data Protection**:
- **Token Hashing**: Payment tokens stored as secure hashes
- **Encryption**: Sensitive data encrypted at rest
- **Access Logging**: All data access logged for audit
- **Input Validation**: Comprehensive input validation and sanitization
- **Rate Limiting**: API rate limiting to prevent abuse

### **📦 Deployment Architecture**

**Container Setup**:
- **Multi-stage Docker builds** for optimized production images
- **Environment-based configuration** via environment variables
- **Health checks** for container orchestration
- **Resource limits** for memory and CPU usage

**Environment Configuration**:
- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: High-availability production deployment

**Monitoring & Logging**:
- **Application Logs**: Structured logging with Winston
- **Performance Metrics**: Custom metrics for business operations
- **Error Tracking**: Comprehensive error logging and alerting
- **Health Monitoring**: Service health checks and uptime monitoring

### **🔄 Data Flow Architecture**

**Webhook Processing Flow**:
1. Payment provider sends webhook to `/webhook/payment`
2. Webhook service validates and parses payload
3. Customer data extracted and normalized
4. Payment token created or updated
5. Audit log entry created
6. Real-time notifications sent to subscribers

**Customer Management Flow**:
1. Admin creates/updates customer via GraphQL
2. Input validation and authorization checks
3. Database transaction with audit logging
4. Real-time updates propagated
5. External systems notified via webhooks

### **🧪 Testing Strategy**

**Test Types**:
- **Unit Tests**: Individual function and method testing
- **Integration Tests**: Database and API integration testing
- **E2E Tests**: Complete workflow testing
- **Performance Tests**: Load and stress testing

**Test Configuration**:
- **Jest**: Primary testing framework
- **Test Database**: Isolated test database for integration tests
- **Mocking**: External service mocking for reliable tests
- **Coverage**: Comprehensive code coverage reporting

### **🚀 Development Workflow**

**Local Development**:
1. Clone repository and install dependencies
2. Set up local PostgreSQL database
3. Run database migrations
4. Start development server with hot reload
5. Use GraphQL playground for API testing

**Code Quality**:
- **TypeScript**: Strong typing for better code quality
- **ESLint**: Code linting and style enforcement
- **Prettier**: Automatic code formatting
- **Husky**: Pre-commit hooks for quality checks

**Deployment Process**:
1. Code pushed to repository
2. Automated tests run in CI/CD pipeline
3. Docker image built and tagged
4. Image deployed to target environment
5. Health checks verify successful deployment
