"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionResponseType = exports.UserDetailsType = exports.PhoneDetailsType = exports.PaymentDetailsType = exports.OtpVerificationInput = exports.TransactionInput = exports.TransactionResponseDto = exports.UserDetailsDto = exports.PaymentDetailsDto = exports.OtpVerificationRequestDto = exports.TransactionRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const graphql_1 = require("@nestjs/graphql");
class TransactionRequestDto {
    transactionId;
    amount;
    currency;
    description;
}
exports.TransactionRequestDto = TransactionRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique transaction identifier',
        example: 'txn_*********0abcdef'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TransactionRequestDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Transaction amount',
        example: 100.50
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TransactionRequestDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        example: 'USD'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TransactionRequestDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Transaction description',
        example: 'Payment for services',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TransactionRequestDto.prototype, "description", void 0);
class OtpVerificationRequestDto {
    transactionId;
    phoneNumber;
    otp;
}
exports.OtpVerificationRequestDto = OtpVerificationRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Transaction identifier',
        example: 'txn_*********0abcdef'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], OtpVerificationRequestDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phone number for OTP verification',
        example: '+*********0'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Matches)(/^\+[1-9]\d{1,14}$/, { message: 'Phone number must be in international format' }),
    __metadata("design:type", String)
], OtpVerificationRequestDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'OTP code (6 digits)',
        example: '123456'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Matches)(/^\d{6}$/, { message: 'OTP must be 6 digits' }),
    __metadata("design:type", String)
], OtpVerificationRequestDto.prototype, "otp", void 0);
class PaymentDetailsDto {
    routingNumber;
    cvv;
    accountNumber;
    expiryDate;
}
exports.PaymentDetailsDto = PaymentDetailsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bank routing number',
        example: '*********'
    }),
    __metadata("design:type", String)
], PaymentDetailsDto.prototype, "routingNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CVV security code',
        example: '123'
    }),
    __metadata("design:type", String)
], PaymentDetailsDto.prototype, "cvv", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account number',
        example: '****1234'
    }),
    __metadata("design:type", String)
], PaymentDetailsDto.prototype, "accountNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Card expiry date',
        example: '12/25'
    }),
    __metadata("design:type", String)
], PaymentDetailsDto.prototype, "expiryDate", void 0);
class UserDetailsDto {
    id;
    email;
    firstName;
    lastName;
    phone;
    country;
    verifiedEmail;
    verifiedPhone;
    role;
    createdAt;
    partnerId;
    mfaEnabled;
    active;
    accountId;
    isAdmin;
    permissions;
}
exports.UserDetailsDto = UserDetailsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID' }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User email' }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'First name', required: false }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last name', required: false }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phone information',
        type: 'object',
        properties: {
            number: { type: 'string', description: 'Full phone number' },
            masked: { type: 'string', description: 'Masked phone number' }
        }
    }),
    __metadata("design:type", Object)
], UserDetailsDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Country', required: false }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email verification status', required: false }),
    __metadata("design:type", Boolean)
], UserDetailsDto.prototype, "verifiedEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Phone verification status', required: false }),
    __metadata("design:type", Boolean)
], UserDetailsDto.prototype, "verifiedPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User role', required: false }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account creation date', required: false }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Partner ID', required: false }),
    __metadata("design:type", Object)
], UserDetailsDto.prototype, "partnerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'MFA enabled status', required: false }),
    __metadata("design:type", Boolean)
], UserDetailsDto.prototype, "mfaEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account active status', required: false }),
    __metadata("design:type", Boolean)
], UserDetailsDto.prototype, "active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account ID', required: false }),
    __metadata("design:type", String)
], UserDetailsDto.prototype, "accountId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Admin status' }),
    __metadata("design:type", Boolean)
], UserDetailsDto.prototype, "isAdmin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User permissions', type: [String] }),
    __metadata("design:type", Array)
], UserDetailsDto.prototype, "permissions", void 0);
class TransactionResponseDto {
    transactionId;
    status;
    message;
    userDetails;
    paymentDetails;
}
exports.TransactionResponseDto = TransactionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Transaction identifier',
        example: 'txn_*********0abcdef'
    }),
    __metadata("design:type", String)
], TransactionResponseDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Transaction status',
        enum: ['pending', 'otp_sent', 'verified', 'completed', 'failed'],
        example: 'otp_sent'
    }),
    __metadata("design:type", String)
], TransactionResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status message',
        example: 'OTP sent to +***-***-1234'
    }),
    __metadata("design:type", String)
], TransactionResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User details with nested phone information',
        type: UserDetailsDto,
        required: false
    }),
    __metadata("design:type", UserDetailsDto)
], TransactionResponseDto.prototype, "userDetails", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Payment details (only returned after successful OTP verification)',
        type: PaymentDetailsDto,
        required: false
    }),
    __metadata("design:type", PaymentDetailsDto)
], TransactionResponseDto.prototype, "paymentDetails", void 0);
let TransactionInput = class TransactionInput {
    transactionId;
    amount;
    currency;
    description;
};
exports.TransactionInput = TransactionInput;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { description: 'Unique transaction identifier' }),
    __metadata("design:type", String)
], TransactionInput.prototype, "transactionId", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Float, { description: 'Transaction amount' }),
    __metadata("design:type", Number)
], TransactionInput.prototype, "amount", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Currency code' }),
    __metadata("design:type", String)
], TransactionInput.prototype, "currency", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Transaction description', nullable: true }),
    __metadata("design:type", String)
], TransactionInput.prototype, "description", void 0);
exports.TransactionInput = TransactionInput = __decorate([
    (0, graphql_1.InputType)()
], TransactionInput);
let OtpVerificationInput = class OtpVerificationInput {
    transactionId;
    phoneNumber;
    otp;
};
exports.OtpVerificationInput = OtpVerificationInput;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { description: 'Transaction identifier' }),
    __metadata("design:type", String)
], OtpVerificationInput.prototype, "transactionId", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Phone number for OTP verification' }),
    __metadata("design:type", String)
], OtpVerificationInput.prototype, "phoneNumber", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'OTP code (6 digits)' }),
    __metadata("design:type", String)
], OtpVerificationInput.prototype, "otp", void 0);
exports.OtpVerificationInput = OtpVerificationInput = __decorate([
    (0, graphql_1.InputType)()
], OtpVerificationInput);
let PaymentDetailsType = class PaymentDetailsType {
    routingNumber;
    cvv;
    accountNumber;
    expiryDate;
};
exports.PaymentDetailsType = PaymentDetailsType;
__decorate([
    (0, graphql_1.Field)({ description: 'Bank routing number' }),
    __metadata("design:type", String)
], PaymentDetailsType.prototype, "routingNumber", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'CVV security code' }),
    __metadata("design:type", String)
], PaymentDetailsType.prototype, "cvv", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Account number' }),
    __metadata("design:type", String)
], PaymentDetailsType.prototype, "accountNumber", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Card expiry date' }),
    __metadata("design:type", String)
], PaymentDetailsType.prototype, "expiryDate", void 0);
exports.PaymentDetailsType = PaymentDetailsType = __decorate([
    (0, graphql_1.ObjectType)()
], PaymentDetailsType);
let PhoneDetailsType = class PhoneDetailsType {
    number;
    masked;
};
exports.PhoneDetailsType = PhoneDetailsType;
__decorate([
    (0, graphql_1.Field)({ description: 'Full phone number' }),
    __metadata("design:type", String)
], PhoneDetailsType.prototype, "number", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Masked phone number' }),
    __metadata("design:type", String)
], PhoneDetailsType.prototype, "masked", void 0);
exports.PhoneDetailsType = PhoneDetailsType = __decorate([
    (0, graphql_1.ObjectType)()
], PhoneDetailsType);
let UserDetailsType = class UserDetailsType {
    id;
    email;
    password;
    firstName;
    lastName;
    phone;
    country;
    verifiedEmail;
    verifiedPhone;
    totpSecret;
    role;
    createdAt;
    partnerId;
    mfaEnabled;
    active;
    accountId;
    isAdmin;
    permissions;
};
exports.UserDetailsType = UserDetailsType;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { description: 'User ID' }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'User email' }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "email", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'User password', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "password", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'First name', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "firstName", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Last name', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "lastName", void 0);
__decorate([
    (0, graphql_1.Field)(() => PhoneDetailsType, { description: 'Phone information', nullable: true }),
    __metadata("design:type", PhoneDetailsType)
], UserDetailsType.prototype, "phone", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Country', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "country", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Email verification status', nullable: true }),
    __metadata("design:type", Boolean)
], UserDetailsType.prototype, "verifiedEmail", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Phone verification status', nullable: true }),
    __metadata("design:type", Boolean)
], UserDetailsType.prototype, "verifiedPhone", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'TOTP secret', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "totpSecret", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'User role', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "role", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Account creation date', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Partner ID', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "partnerId", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'MFA enabled status', nullable: true }),
    __metadata("design:type", Boolean)
], UserDetailsType.prototype, "mfaEnabled", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Account active status', nullable: true }),
    __metadata("design:type", Boolean)
], UserDetailsType.prototype, "active", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Account ID', nullable: true }),
    __metadata("design:type", String)
], UserDetailsType.prototype, "accountId", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Admin status' }),
    __metadata("design:type", Boolean)
], UserDetailsType.prototype, "isAdmin", void 0);
__decorate([
    (0, graphql_1.Field)(() => [String], { description: 'User permissions' }),
    __metadata("design:type", Array)
], UserDetailsType.prototype, "permissions", void 0);
exports.UserDetailsType = UserDetailsType = __decorate([
    (0, graphql_1.ObjectType)()
], UserDetailsType);
let TransactionResponseType = class TransactionResponseType {
    transactionId;
    status;
    message;
    userDetails;
    paymentDetails;
};
exports.TransactionResponseType = TransactionResponseType;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { description: 'Transaction identifier' }),
    __metadata("design:type", String)
], TransactionResponseType.prototype, "transactionId", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Transaction status' }),
    __metadata("design:type", String)
], TransactionResponseType.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)({ description: 'Status message' }),
    __metadata("design:type", String)
], TransactionResponseType.prototype, "message", void 0);
__decorate([
    (0, graphql_1.Field)(() => UserDetailsType, { description: 'User details with nested phone information', nullable: true }),
    __metadata("design:type", UserDetailsType)
], TransactionResponseType.prototype, "userDetails", void 0);
__decorate([
    (0, graphql_1.Field)(() => PaymentDetailsType, { description: 'Payment details (only returned after successful OTP verification)', nullable: true }),
    __metadata("design:type", PaymentDetailsType)
], TransactionResponseType.prototype, "paymentDetails", void 0);
exports.TransactionResponseType = TransactionResponseType = __decorate([
    (0, graphql_1.ObjectType)()
], TransactionResponseType);
//# sourceMappingURL=transaction.dto.js.map