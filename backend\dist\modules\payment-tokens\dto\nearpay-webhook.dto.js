"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentTokenResponseDto = exports.NearPayWebhookDto = exports.NearPayMerchantData = exports.NearPayCardData = exports.NearPayTransactionType = exports.NearPayTransactionStatus = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
var NearPayTransactionStatus;
(function (NearPayTransactionStatus) {
    NearPayTransactionStatus["APPROVED"] = "APPROVED";
    NearPayTransactionStatus["DECLINED"] = "DECLINED";
    NearPayTransactionStatus["CANCELLED"] = "CANCELLED";
    NearPayTransactionStatus["REFUNDED"] = "REFUNDED";
    NearPayTransactionStatus["PARTIALLY_REFUNDED"] = "PARTIALLY_REFUNDED";
})(NearPayTransactionStatus || (exports.NearPayTransactionStatus = NearPayTransactionStatus = {}));
var NearPayTransactionType;
(function (NearPayTransactionType) {
    NearPayTransactionType["SALE"] = "SALE";
    NearPayTransactionType["REFUND"] = "REFUND";
    NearPayTransactionType["VOID"] = "VOID";
    NearPayTransactionType["AUTH"] = "AUTH";
    NearPayTransactionType["CAPTURE"] = "CAPTURE";
})(NearPayTransactionType || (exports.NearPayTransactionType = NearPayTransactionType = {}));
class NearPayCardData {
    last4;
    brand;
    cardType;
    expiryMonth;
    expiryYear;
    cardholderName;
}
exports.NearPayCardData = NearPayCardData;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last 4 digits of the card' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayCardData.prototype, "last4", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Card brand (Visa, Mastercard, etc.)' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayCardData.prototype, "brand", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Card type (Credit, Debit, etc.)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayCardData.prototype, "cardType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Card expiry month', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayCardData.prototype, "expiryMonth", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Card expiry year', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayCardData.prototype, "expiryYear", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Cardholder name', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayCardData.prototype, "cardholderName", void 0);
class NearPayMerchantData {
    merchantId;
    terminalId;
    merchantName;
}
exports.NearPayMerchantData = NearPayMerchantData;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Merchant ID' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayMerchantData.prototype, "merchantId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Terminal ID' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayMerchantData.prototype, "terminalId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Merchant name', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayMerchantData.prototype, "merchantName", void 0);
class NearPayWebhookDto {
    transactionId;
    referenceNumber;
    amount;
    currency;
    status;
    type;
    authCode;
    card;
    merchant;
    timestamp;
    customerId;
    customerEmail;
    customerPhone;
    metadata;
    signature;
    eventType;
}
exports.NearPayWebhookDto = NearPayWebhookDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique transaction ID from NearPay' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction reference number', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "referenceNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction amount in cents' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], NearPayWebhookDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency code (e.g., USD, SAR)' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction status', enum: NearPayTransactionStatus }),
    (0, class_validator_1.IsEnum)(NearPayTransactionStatus),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction type', enum: NearPayTransactionType }),
    (0, class_validator_1.IsEnum)(NearPayTransactionType),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Authorization code', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "authCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Card information' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => NearPayCardData),
    __metadata("design:type", NearPayCardData)
], NearPayWebhookDto.prototype, "card", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Merchant information' }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => NearPayMerchantData),
    __metadata("design:type", NearPayMerchantData)
], NearPayWebhookDto.prototype, "merchant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Transaction timestamp in ISO 8601 format',
        example: '2024-01-15T10:30:00.000Z'
    }),
    (0, class_validator_1.IsDateString)({}, { message: 'timestamp must be a valid ISO 8601 date string' }),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/)) {
                return value.endsWith('Z') ? value : value + 'Z';
            }
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
                return date.toISOString();
            }
        }
        return value;
    }),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer identifier (email, phone, or external ID)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "customerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer email', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "customerEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer phone', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "customerPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Additional metadata', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], NearPayWebhookDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Webhook signature for verification', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "signature", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Webhook event type', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], NearPayWebhookDto.prototype, "eventType", void 0);
class PaymentTokenResponseDto {
    id;
    customerId;
    paymentProvider;
    tokenType;
    status;
    maskedInfo;
    paymentBrand;
    expiresAt;
    lastUsedAt;
    usageCount;
    createdAt;
}
exports.PaymentTokenResponseDto = PaymentTokenResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Token ID' }),
    __metadata("design:type", String)
], PaymentTokenResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Customer ID' }),
    __metadata("design:type", String)
], PaymentTokenResponseDto.prototype, "customerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Payment provider' }),
    __metadata("design:type", String)
], PaymentTokenResponseDto.prototype, "paymentProvider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Token type' }),
    __metadata("design:type", String)
], PaymentTokenResponseDto.prototype, "tokenType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Token status' }),
    __metadata("design:type", String)
], PaymentTokenResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Masked payment info', required: false }),
    __metadata("design:type", String)
], PaymentTokenResponseDto.prototype, "maskedInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Payment brand', required: false }),
    __metadata("design:type", String)
], PaymentTokenResponseDto.prototype, "paymentBrand", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Expiration date', required: false }),
    __metadata("design:type", Date)
], PaymentTokenResponseDto.prototype, "expiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last used date', required: false }),
    __metadata("design:type", Date)
], PaymentTokenResponseDto.prototype, "lastUsedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Usage count' }),
    __metadata("design:type", Number)
], PaymentTokenResponseDto.prototype, "usageCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Created date' }),
    __metadata("design:type", Date)
], PaymentTokenResponseDto.prototype, "createdAt", void 0);
//# sourceMappingURL=nearpay-webhook.dto.js.map