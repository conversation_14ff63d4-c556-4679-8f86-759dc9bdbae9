{"version": 3, "file": "nearpay-webhook.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/payment-tokens/dto/nearpay-webhook.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAiH;AACjH,yDAAoD;AACpD,6CAA8C;AAE9C,IAAY,wBAMX;AAND,WAAY,wBAAwB;IAClC,iDAAqB,CAAA;IACrB,iDAAqB,CAAA;IACrB,mDAAuB,CAAA;IACvB,iDAAqB,CAAA;IACrB,qEAAyC,CAAA;AAC3C,CAAC,EANW,wBAAwB,wCAAxB,wBAAwB,QAMnC;AAED,IAAY,sBAMX;AAND,WAAY,sBAAsB;IAChC,uCAAa,CAAA;IACb,2CAAiB,CAAA;IACjB,uCAAa,CAAA;IACb,uCAAa,CAAA;IACb,6CAAmB,CAAA;AACrB,CAAC,EANW,sBAAsB,sCAAtB,sBAAsB,QAMjC;AAED,MAAa,eAAe;IAG1B,KAAK,CAAS;IAId,KAAK,CAAS;IAKd,QAAQ,CAAU;IAKlB,WAAW,CAAU;IAKrB,UAAU,CAAU;IAKpB,cAAc,CAAU;CACzB;AA5BD,0CA4BC;AAzBC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACzD,IAAA,0BAAQ,GAAE;;8CACG;AAId;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qCAAqC,EAAE,CAAC;IACnE,IAAA,0BAAQ,GAAE;;8CACG;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iCAAiC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACO;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACU;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACS;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACa;AAG1B,MAAa,mBAAmB;IAG9B,UAAU,CAAS;IAInB,UAAU,CAAS;IAKnB,YAAY,CAAU;CACvB;AAbD,kDAaC;AAVC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC3C,IAAA,0BAAQ,GAAE;;uDACQ;AAInB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC3C,IAAA,0BAAQ,GAAE;;uDACQ;AAKnB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACW;AAGxB,MAAa,iBAAiB;IAG5B,aAAa,CAAS;IAKtB,eAAe,CAAU;IAIzB,MAAM,CAAS;IAIf,QAAQ,CAAS;IAIjB,MAAM,CAA2B;IAIjC,IAAI,CAAyB;IAK7B,QAAQ,CAAU;IAKlB,IAAI,CAAkB;IAKtB,QAAQ,CAAsB;IAsB9B,SAAS,CAAS;IAKlB,UAAU,CAAU;IAKpB,aAAa,CAAU;IAKvB,aAAa,CAAU;IAKvB,QAAQ,CAAuB;IAK/B,SAAS,CAAU;IAKnB,SAAS,CAAU;CACpB;AA5FD,8CA4FC;AAzFC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oCAAoC,EAAE,CAAC;IAClE,IAAA,0BAAQ,GAAE;;wDACW;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC7E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0DACc;AAIzB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,CAAC;IAC3D,IAAA,0BAAQ,GAAE;;iDACI;AAIf;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IAC9D,IAAA,0BAAQ,GAAE;;mDACM;AAIjB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;IAClF,IAAA,wBAAM,EAAC,wBAAwB,CAAC;;iDACA;AAIjC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC;IAC9E,IAAA,wBAAM,EAAC,sBAAsB,CAAC;;+CACF;AAK7B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACO;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAChD,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC;8BACtB,eAAe;+CAAC;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACpD,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC;8BACtB,mBAAmB;mDAAC;AAsB9B;IApBC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0CAA0C;QACvD,OAAO,EAAE,0BAA0B;KACpC,CAAC;IACD,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,gDAAgD,EAAE,CAAC;IAC/E,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;QAEvB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAE9B,IAAI,KAAK,CAAC,KAAK,CAAC,mDAAmD,CAAC,EAAE,CAAC;gBACrE,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;YACnD,CAAC;YAED,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;;oDACgB;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oDAAoD,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACnG,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACS;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACY;AAKvB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACY;AAKvB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACpE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACoB;AAK/B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oCAAoC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACnF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACQ;AAKnB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACQ;AAGrB,MAAa,uBAAuB;IAElC,EAAE,CAAS;IAGX,UAAU,CAAS;IAGnB,eAAe,CAAS;IAGxB,SAAS,CAAS;IAGlB,MAAM,CAAS;IAGf,UAAU,CAAU;IAGpB,YAAY,CAAU;IAGtB,SAAS,CAAQ;IAGjB,UAAU,CAAQ;IAGlB,UAAU,CAAS;IAGnB,SAAS,CAAO;CACjB;AAjCD,0DAiCC;AA/BC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;;mDAC9B;AAGX;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;;2DACzB;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;;gEACzB;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;;0DACzB;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;;uDAC9B;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;2DACjD;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;6DACzC;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;8BACrD,IAAI;0DAAC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;8BACnD,IAAI;2DAAC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;;2DACzB;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;8BAClC,IAAI;0DAAC"}