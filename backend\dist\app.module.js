"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const config_module_1 = require("./config/config.module");
const auth_shared_module_1 = require("./modules/auth-shared/auth-shared.module");
const health_1 = require("./modules/health");
const webhook_module_1 = require("./webhook/webhook.module");
const graphql_module_1 = require("./modules/graphql/graphql.module");
const prisma_module_1 = require("./prisma/prisma.module");
const customers_query_module_1 = require("./modules/customers-query/customers-query.module");
const customers_management_module_1 = require("./modules/customers-management/customers-management.module");
const customers_verification_module_1 = require("./modules/customers-verification/customers-verification.module");
const customers_admin_module_1 = require("./modules/customers-admin/customers-admin.module");
const customers_audit_module_1 = require("./modules/customers-audit/customers-audit.module");
const customers_access_control_module_1 = require("./modules/customers-access-control/customers-access-control.module");
const customers_shared_module_1 = require("./modules/customers-shared/customers-shared.module");
const transaction_module_1 = require("./modules/transactions/transaction.module");
const payment_tokens_module_1 = require("./modules/payment-tokens/payment-tokens.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_module_1.ConfigModule,
            prisma_module_1.PrismaModule,
            graphql_module_1.GraphQLConfigModule,
            customers_query_module_1.CustomersQueryModule,
            customers_management_module_1.CustomersManagementModule,
            customers_verification_module_1.CustomersVerificationModule,
            customers_admin_module_1.CustomersAdminModule,
            customers_audit_module_1.CustomersAuditModule,
            customers_access_control_module_1.CustomersAccessControlModule,
            customers_shared_module_1.CustomersSharedModule,
            transaction_module_1.TransactionModule,
            payment_tokens_module_1.PaymentTokensModule,
            webhook_module_1.WebhookModule,
            auth_shared_module_1.AuthSharedModule,
            axios_1.HttpModule,
            health_1.HealthModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map