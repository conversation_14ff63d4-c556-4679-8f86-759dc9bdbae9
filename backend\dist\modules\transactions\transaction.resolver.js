"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TransactionResolver_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const auth_shared_1 = require("../auth-shared");
const transaction_service_1 = require("./transaction.service");
const transaction_dto_1 = require("./dto/transaction.dto");
let TransactionResolver = TransactionResolver_1 = class TransactionResolver {
    transactionService;
    logger = new common_1.Logger(TransactionResolver_1.name);
    constructor(transactionService) {
        this.transactionService = transactionService;
    }
    async initiateTransaction(user, input) {
        this.logger.log(`GraphQL: Initiating transaction for user: ${user.email}`);
        const transactionData = {
            transactionId: input.transactionId,
            amount: input.amount,
            currency: input.currency,
            description: input.description,
        };
        const result = await this.transactionService.initiateTransaction(user, transactionData);
        return {
            transactionId: result.transactionId,
            status: result.status,
            message: result.message,
            userDetails: result.userDetails ? {
                id: result.userDetails.id,
                email: result.userDetails.email,
                password: result.userDetails.password,
                firstName: result.userDetails.firstName,
                lastName: result.userDetails.lastName,
                phone: result.userDetails.phone,
                country: result.userDetails.country,
                verifiedEmail: result.userDetails.verifiedEmail,
                verifiedPhone: result.userDetails.verifiedPhone,
                totpSecret: result.userDetails.totpSecret,
                role: result.userDetails.role,
                createdAt: result.userDetails.createdAt,
                partnerId: Array.isArray(result.userDetails.partnerId)
                    ? result.userDetails.partnerId.join(',')
                    : result.userDetails.partnerId,
                mfaEnabled: result.userDetails.mfaEnabled,
                active: result.userDetails.active,
                accountId: result.userDetails.accountId,
                isAdmin: result.userDetails.isAdmin,
                permissions: result.userDetails.permissions,
            } : undefined,
            paymentDetails: result.paymentDetails,
        };
    }
    async verifyOtpAndCompleteTransaction(user, input) {
        this.logger.log(`GraphQL: Verifying OTP for transaction: ${input.transactionId}`);
        const otpData = {
            transactionId: input.transactionId,
            phoneNumber: input.phoneNumber,
            otp: input.otp,
        };
        const result = await this.transactionService.verifyOtpAndCompleteTransaction(user, otpData);
        return {
            transactionId: result.transactionId,
            status: result.status,
            message: result.message,
            userDetails: result.userDetails ? {
                id: result.userDetails.id,
                email: result.userDetails.email,
                password: result.userDetails.password,
                firstName: result.userDetails.firstName,
                lastName: result.userDetails.lastName,
                phone: result.userDetails.phone,
                country: result.userDetails.country,
                verifiedEmail: result.userDetails.verifiedEmail,
                verifiedPhone: result.userDetails.verifiedPhone,
                totpSecret: result.userDetails.totpSecret,
                role: result.userDetails.role,
                createdAt: result.userDetails.createdAt,
                partnerId: Array.isArray(result.userDetails.partnerId)
                    ? result.userDetails.partnerId.join(',')
                    : result.userDetails.partnerId,
                mfaEnabled: result.userDetails.mfaEnabled,
                active: result.userDetails.active,
                accountId: result.userDetails.accountId,
                isAdmin: result.userDetails.isAdmin,
                permissions: result.userDetails.permissions,
            } : undefined,
            paymentDetails: result.paymentDetails,
        };
    }
};
exports.TransactionResolver = TransactionResolver;
__decorate([
    (0, graphql_1.Mutation)(() => transaction_dto_1.TransactionResponseType, {
        name: 'initiateTransaction',
        description: 'Initiate a transaction and send OTP to user\'s phone number'
    }),
    __param(0, (0, auth_shared_1.getCurrentUser)()),
    __param(1, (0, graphql_1.Args)('input', { type: () => transaction_dto_1.TransactionInput })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, transaction_dto_1.TransactionInput]),
    __metadata("design:returntype", Promise)
], TransactionResolver.prototype, "initiateTransaction", null);
__decorate([
    (0, graphql_1.Mutation)(() => transaction_dto_1.TransactionResponseType, {
        name: 'verifyOtpAndCompleteTransaction',
        description: 'Verify OTP and complete transaction, returning payment details on success'
    }),
    __param(0, (0, auth_shared_1.getCurrentUser)()),
    __param(1, (0, graphql_1.Args)('input', { type: () => transaction_dto_1.OtpVerificationInput })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, transaction_dto_1.OtpVerificationInput]),
    __metadata("design:returntype", Promise)
], TransactionResolver.prototype, "verifyOtpAndCompleteTransaction", null);
exports.TransactionResolver = TransactionResolver = TransactionResolver_1 = __decorate([
    (0, graphql_1.Resolver)(),
    (0, common_1.UseGuards)(auth_shared_1.ApiGuard),
    __metadata("design:paramtypes", [transaction_service_1.TransactionService])
], TransactionResolver);
//# sourceMappingURL=transaction.resolver.js.map