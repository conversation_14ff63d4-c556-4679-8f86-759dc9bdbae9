"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var CustomersAccessControlService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersAccessControlService = void 0;
const common_1 = require("@nestjs/common");
let CustomersAccessControlService = CustomersAccessControlService_1 = class CustomersAccessControlService {
    logger = new common_1.Logger(CustomersAccessControlService_1.name);
    checkPermission(user, permission) {
        if (user.isAdmin || (user.permissions && user.permissions.includes('*'))) {
            return true;
        }
        return user.permissions ? user.permissions.includes(permission) : false;
    }
    checkAdminAccess(user) {
        return user.isAdmin || false;
    }
    checkCustomerAccess(user, customer, options = {}) {
        if (user.isAdmin) {
            return true;
        }
        if (options.requireAdmin) {
            return false;
        }
        if (options.requirePermission && !this.checkPermission(user, options.requirePermission)) {
            return false;
        }
        if (options.allowSelfAccess && customer.externalId === user.id) {
            return true;
        }
        if (options.allowPartnerAccess && user.partnerId) {
            return true;
        }
        if (options.allowAccountAccess && user.accountId) {
            return true;
        }
        return false;
    }
    applyAccessControlFilters(user, where = {}, options = {}) {
        if (user.isAdmin) {
            return where;
        }
        if (options.requireAdmin) {
            return { ...where, id: 'impossible-id' };
        }
        if (options.requirePermission && !this.checkPermission(user, options.requirePermission)) {
            return { ...where, id: 'impossible-id' };
        }
        const accessFilters = [];
        if (options.allowSelfAccess && user.id) {
            accessFilters.push({ externalId: user.id });
        }
        if (options.allowPartnerAccess && user.partnerId) {
        }
        if (options.allowAccountAccess && user.accountId) {
        }
        if (accessFilters.length === 0) {
            return { ...where, id: 'impossible-id' };
        }
        return {
            ...where,
            OR: accessFilters,
        };
    }
    validateCustomerAccess(user, customer, action, options = {}) {
        if (!this.checkCustomerAccess(user, customer, options)) {
            this.logger.warn(`Access denied for user ${user.id} to ${action} customer ${customer.id}`);
            throw new common_1.ForbiddenException(`Access denied to ${action} this customer`);
        }
    }
    validatePermission(user, permission, action) {
        if (!this.checkPermission(user, permission)) {
            this.logger.warn(`Permission denied for user ${user.id} to ${action} (requires ${permission})`);
            throw new common_1.ForbiddenException(`Permission denied to ${action}`);
        }
    }
    validateAdminAccess(user, action) {
        if (!this.checkAdminAccess(user)) {
            this.logger.warn(`Admin access denied for user ${user.id} to ${action}`);
            throw new common_1.ForbiddenException(`Admin access required to ${action}`);
        }
    }
    getDefaultOptions(operation) {
        switch (operation) {
            case 'read':
                return {
                    requirePermission: 'read:customers',
                    allowSelfAccess: true,
                    allowPartnerAccess: true,
                    allowAccountAccess: true,
                };
            case 'write':
                return {
                    requirePermission: 'write:customers',
                    allowSelfAccess: false,
                    allowPartnerAccess: true,
                    allowAccountAccess: true,
                };
            case 'delete':
                return {
                    requirePermission: 'delete:customers',
                    allowSelfAccess: false,
                    allowPartnerAccess: false,
                    allowAccountAccess: false,
                };
            case 'admin':
                return {
                    requireAdmin: true,
                };
            default:
                return {};
        }
    }
    filterCustomerData(user, customer, options = {}) {
        if (user.isAdmin) {
            return customer;
        }
        const filteredCustomer = { ...customer };
        if (!this.checkPermission(user, 'read:customer:sensitive')) {
            filteredCustomer.taxId = null;
            filteredCustomer.notes = null;
        }
        if (!this.checkPermission(user, 'read:customer:contacts')) {
            filteredCustomer.contacts = [];
        }
        if (!this.checkPermission(user, 'read:customer:addresses')) {
            filteredCustomer.addresses = [];
        }
        if (!this.checkPermission(user, 'read:customer:preferences')) {
            filteredCustomer.preferences = [];
        }
        return filteredCustomer;
    }
    checkBulkOperationAccess(user, operation) {
        if (user.isAdmin) {
            return true;
        }
        const permission = operation === 'update' ? 'bulk:update:customers' : 'bulk:delete:customers';
        return this.checkPermission(user, permission);
    }
    validateBulkOperationAccess(user, operation) {
        if (!this.checkBulkOperationAccess(user, operation)) {
            this.logger.warn(`Bulk ${operation} access denied for user ${user.id}`);
            throw new common_1.ForbiddenException(`Permission denied for bulk ${operation} operations`);
        }
    }
};
exports.CustomersAccessControlService = CustomersAccessControlService;
exports.CustomersAccessControlService = CustomersAccessControlService = CustomersAccessControlService_1 = __decorate([
    (0, common_1.Injectable)()
], CustomersAccessControlService);
//# sourceMappingURL=customers-access-control.service.js.map