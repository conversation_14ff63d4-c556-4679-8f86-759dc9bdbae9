"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentTokensController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentTokensController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_tokens_service_1 = require("./payment-tokens.service");
const nearpay_webhook_dto_1 = require("./dto/nearpay-webhook.dto");
let PaymentTokensController = PaymentTokensController_1 = class PaymentTokensController {
    paymentTokensService;
    logger = new common_1.Logger(PaymentTokensController_1.name);
    constructor(paymentTokensService) {
        this.paymentTokensService = paymentTokensService;
    }
    async handleNearPayWebhook(webhookData, request) {
        this.logger.log(`Received NearPay webhook for transaction: ${webhookData.transactionId}`);
        const clientIp = request.ip || request.headers['x-forwarded-for'] || 'unknown';
        this.logger.debug(`Webhook data: ${JSON.stringify({
            transactionId: webhookData.transactionId,
            amount: webhookData.amount,
            currency: webhookData.currency,
            status: webhookData.status,
            cardLast4: webhookData.card.last4,
            cardBrand: webhookData.card.brand,
            merchantId: webhookData.merchant.merchantId,
            customerEmail: webhookData.customerEmail,
            customerPhone: webhookData.customerPhone,
        })}`);
        return await this.paymentTokensService.processNearPayWebhook(webhookData, clientIp);
    }
    async getCustomerPaymentTokens(customerId) {
        this.logger.log(`Retrieving payment tokens for customer: ${customerId}`);
        return await this.paymentTokensService.getCustomerPaymentTokens(customerId);
    }
    async revokePaymentToken(tokenId, customerId) {
        this.logger.log(`Revoking payment token: ${tokenId} for customer: ${customerId}`);
        await this.paymentTokensService.revokePaymentToken(tokenId, customerId);
    }
    async testNearPayWebhook(request) {
        const testWebhookData = {
            transactionId: `test_${Date.now()}`,
            referenceNumber: `REF${Date.now()}`,
            amount: 10000,
            currency: 'USD',
            status: 'APPROVED',
            type: 'SALE',
            authCode: 'AUTH123',
            card: {
                last4: '1234',
                brand: 'Visa',
                cardType: 'Credit',
                expiryMonth: '12',
                expiryYear: '2025',
                cardholderName: 'John Doe',
            },
            merchant: {
                merchantId: 'MERCHANT_001',
                terminalId: 'TERMINAL_001',
                merchantName: 'Test Merchant',
            },
            timestamp: new Date().toISOString(),
            customerEmail: '<EMAIL>',
            eventType: 'transaction.approved',
        };
        this.logger.log('Processing test NearPay webhook');
        const clientIp = request.ip || 'test';
        return await this.paymentTokensService.processNearPayWebhook(testWebhookData, clientIp);
    }
    async testTimestampValidation() {
        const examples = [
            { format: 'ISO 8601 with Z', value: new Date().toISOString(), valid: true },
            { format: 'ISO 8601 without Z', value: new Date().toISOString().slice(0, -1), valid: true },
            { format: 'Date string', value: new Date().toString(), valid: false },
            { format: 'Unix timestamp', value: Date.now().toString(), valid: false },
        ];
        return {
            message: 'Timestamp validation examples',
            examples
        };
    }
};
exports.PaymentTokensController = PaymentTokensController;
__decorate([
    (0, common_1.Post)('webhooks/nearpay'),
    (0, swagger_1.ApiOperation)({
        summary: 'NearPay webhook endpoint',
        description: 'Receives transaction data from NearPay and creates/updates payment tokens for customers'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Payment token created/updated successfully',
        type: nearpay_webhook_dto_1.PaymentTokenResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid webhook data or non-approved transaction',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Customer not found',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [nearpay_webhook_dto_1.NearPayWebhookDto, Object]),
    __metadata("design:returntype", Promise)
], PaymentTokensController.prototype, "handleNearPayWebhook", null);
__decorate([
    (0, common_1.Get)('customer/:customerId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get customer payment tokens',
        description: 'Retrieve all active payment tokens for a specific customer'
    }),
    (0, swagger_1.ApiParam)({ name: 'customerId', description: 'Customer UUID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Customer payment tokens retrieved successfully',
        type: [nearpay_webhook_dto_1.PaymentTokenResponseDto],
    }),
    __param(0, (0, common_1.Param)('customerId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentTokensController.prototype, "getCustomerPaymentTokens", null);
__decorate([
    (0, common_1.Delete)(':tokenId/customer/:customerId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Revoke payment token',
        description: 'Revoke a specific payment token for a customer'
    }),
    (0, swagger_1.ApiParam)({ name: 'tokenId', description: 'Payment token UUID' }),
    (0, swagger_1.ApiParam)({ name: 'customerId', description: 'Customer UUID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Payment token revoked successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Payment token not found',
    }),
    __param(0, (0, common_1.Param)('tokenId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('customerId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PaymentTokensController.prototype, "revokePaymentToken", null);
__decorate([
    (0, common_1.Post)('webhooks/nearpay/test'),
    (0, swagger_1.ApiOperation)({
        summary: 'Test NearPay webhook (Development only)',
        description: 'Test endpoint to simulate NearPay webhook calls for development and testing'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Test webhook processed successfully',
        type: nearpay_webhook_dto_1.PaymentTokenResponseDto,
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentTokensController.prototype, "testNearPayWebhook", null);
__decorate([
    (0, common_1.Get)('validate/timestamp'),
    (0, swagger_1.ApiOperation)({
        summary: 'Test timestamp validation',
        description: 'Test endpoint to validate timestamp format'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentTokensController.prototype, "testTimestampValidation", null);
exports.PaymentTokensController = PaymentTokensController = PaymentTokensController_1 = __decorate([
    (0, swagger_1.ApiTags)('Payment Tokens'),
    (0, common_1.Controller)('payment-tokens'),
    __metadata("design:paramtypes", [payment_tokens_service_1.PaymentTokensService])
], PaymentTokensController);
//# sourceMappingURL=payment-tokens.controller.js.map