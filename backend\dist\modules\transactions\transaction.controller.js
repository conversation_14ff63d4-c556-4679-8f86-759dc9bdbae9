"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TransactionController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_shared_1 = require("../auth-shared");
const transaction_service_1 = require("./transaction.service");
const transaction_dto_1 = require("./dto/transaction.dto");
let TransactionController = TransactionController_1 = class TransactionController {
    transactionService;
    logger = new common_1.Logger(TransactionController_1.name);
    constructor(transactionService) {
        this.transactionService = transactionService;
    }
    async initiateTransaction(user, transactionData) {
        this.logger.log(`REST: Initiating transaction for user: ${user.email}`);
        const result = await this.transactionService.initiateTransaction(user, transactionData);
        return {
            transactionId: result.transactionId,
            status: result.status,
            message: result.message,
            userDetails: result.userDetails ? {
                id: result.userDetails.id,
                email: result.userDetails.email,
                firstName: result.userDetails.firstName,
                lastName: result.userDetails.lastName,
                phone: result.userDetails.phone,
                country: result.userDetails.country,
                verifiedEmail: result.userDetails.verifiedEmail,
                verifiedPhone: result.userDetails.verifiedPhone,
                role: result.userDetails.role,
                createdAt: result.userDetails.createdAt,
                partnerId: Array.isArray(result.userDetails.partnerId)
                    ? result.userDetails.partnerId.join(',')
                    : result.userDetails.partnerId,
                mfaEnabled: result.userDetails.mfaEnabled,
                active: result.userDetails.active,
                accountId: result.userDetails.accountId,
                isAdmin: result.userDetails.isAdmin,
                permissions: result.userDetails.permissions,
            } : undefined,
            paymentDetails: result.paymentDetails,
        };
    }
    async verifyOtpAndCompleteTransaction(user, otpData) {
        this.logger.log(`REST: Verifying OTP for transaction: ${otpData.transactionId}`);
        const result = await this.transactionService.verifyOtpAndCompleteTransaction(user, otpData);
        return {
            transactionId: result.transactionId,
            status: result.status,
            message: result.message,
            userDetails: result.userDetails ? {
                id: result.userDetails.id,
                email: result.userDetails.email,
                firstName: result.userDetails.firstName,
                lastName: result.userDetails.lastName,
                phone: result.userDetails.phone,
                country: result.userDetails.country,
                verifiedEmail: result.userDetails.verifiedEmail,
                verifiedPhone: result.userDetails.verifiedPhone,
                role: result.userDetails.role,
                createdAt: result.userDetails.createdAt,
                partnerId: Array.isArray(result.userDetails.partnerId)
                    ? result.userDetails.partnerId.join(',')
                    : result.userDetails.partnerId,
                mfaEnabled: result.userDetails.mfaEnabled,
                active: result.userDetails.active,
                accountId: result.userDetails.accountId,
                isAdmin: result.userDetails.isAdmin,
                permissions: result.userDetails.permissions,
            } : undefined,
            paymentDetails: result.paymentDetails,
        };
    }
};
exports.TransactionController = TransactionController;
__decorate([
    (0, common_1.Post)('initiate'),
    (0, swagger_1.ApiOperation)({
        summary: 'Initiate a transaction and send OTP',
        description: 'Starts a new transaction process by validating the request and sending an OTP to the user\'s registered phone number. Returns user details with nested phone information.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Transaction initiated successfully and OTP sent',
        type: transaction_dto_1.TransactionResponseDto,
        examples: {
            success: {
                summary: 'Successful transaction initiation',
                value: {
                    transactionId: 'txn_*********0abcdef',
                    status: 'otp_sent',
                    message: 'OTP sent to +***-***-1234',
                    userDetails: {
                        id: 'cmca6q7w40000l9015jo4lc5o',
                        email: '<EMAIL>',
                        firstName: 'John',
                        lastName: 'Doe',
                        phone: {
                            number: '+*********0',
                            masked: '+***-***-7890'
                        },
                        country: 'USA',
                        verifiedEmail: false,
                        verifiedPhone: false,
                        role: 'account_user',
                        isAdmin: false,
                        permissions: ['read:customers', 'write:customers']
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid transaction data or missing phone number',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Phone number is required for transaction processing' },
                error: { type: 'string', example: 'Bad Request' },
                statusCode: { type: 'number', example: 400 }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Invalid or missing authentication token',
    }),
    __param(0, (0, auth_shared_1.getCurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, transaction_dto_1.TransactionRequestDto]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "initiateTransaction", null);
__decorate([
    (0, common_1.Post)('verify-otp'),
    (0, swagger_1.ApiOperation)({
        summary: 'Verify OTP and complete transaction',
        description: 'Verifies the OTP code and completes the transaction. Returns payment details (routing number, CVV) upon successful verification. Demo OTP: "123456"'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'OTP verified successfully and transaction completed',
        type: transaction_dto_1.TransactionResponseDto,
        examples: {
            success: {
                summary: 'Successful OTP verification',
                value: {
                    transactionId: 'txn_*********0abcdef',
                    status: 'completed',
                    message: 'Transaction completed successfully',
                    userDetails: {
                        id: 'cmca6q7w40000l9015jo4lc5o',
                        email: '<EMAIL>',
                        firstName: 'John',
                        lastName: 'Doe',
                        phone: {
                            number: '+*********0',
                            masked: '+***-***-7890'
                        },
                        country: 'USA',
                        isAdmin: false,
                        permissions: ['read:customers', 'write:customers']
                    },
                    paymentDetails: {
                        routingNumber: '*********',
                        cvv: '123',
                        accountNumber: '****1234',
                        expiryDate: '12/25'
                    }
                }
            },
            failed: {
                summary: 'Failed OTP verification',
                value: {
                    transactionId: 'txn_*********0abcdef',
                    status: 'failed',
                    message: 'Invalid OTP. Please try again.'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid OTP or transaction data',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Phone number does not match user account or invalid token',
    }),
    __param(0, (0, auth_shared_1.getCurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, transaction_dto_1.OtpVerificationRequestDto]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "verifyOtpAndCompleteTransaction", null);
exports.TransactionController = TransactionController = TransactionController_1 = __decorate([
    (0, swagger_1.ApiTags)('transactions'),
    (0, common_1.Controller)('transactions'),
    (0, common_1.UseGuards)(auth_shared_1.ApiGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [transaction_service_1.TransactionService])
], TransactionController);
//# sourceMappingURL=transaction.controller.js.map