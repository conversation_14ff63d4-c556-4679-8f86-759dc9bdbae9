"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersAccessControlModule = void 0;
const common_1 = require("@nestjs/common");
const customers_access_control_service_1 = require("./customers-access-control.service");
const prisma_module_1 = require("../../prisma/prisma.module");
const config_module_1 = require("../../config/config.module");
let CustomersAccessControlModule = class CustomersAccessControlModule {
};
exports.CustomersAccessControlModule = CustomersAccessControlModule;
exports.CustomersAccessControlModule = CustomersAccessControlModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            config_module_1.ConfigModule,
        ],
        providers: [
            customers_access_control_service_1.CustomersAccessControlService,
        ],
        exports: [
            customers_access_control_service_1.CustomersAccessControlService,
        ],
    })
], CustomersAccessControlModule);
//# sourceMappingURL=customers-access-control.module.js.map