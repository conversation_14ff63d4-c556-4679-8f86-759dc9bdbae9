"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CustomersAuditService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersAuditService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let CustomersAuditService = CustomersAuditService_1 = class CustomersAuditService {
    prisma;
    logger = new common_1.Logger(CustomersAuditService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async logAction(options) {
        this.logger.log(`Creating audit log for ${options.action} on ${options.entity}`);
        try {
            const auditLog = await this.prisma.auditLog.create({
                data: {
                    customerId: options.customerId,
                    action: options.action,
                    entity: options.entity,
                    entityId: options.entityId,
                    oldValues: options.oldValues ? JSON.stringify(options.oldValues) : undefined,
                    newValues: options.newValues ? JSON.stringify(options.newValues) : undefined,
                    description: options.description,
                    metadata: options.metadata ? JSON.stringify(options.metadata) : undefined,
                    ipAddress: options.ipAddress,
                    userAgent: options.userAgent,
                    sessionId: options.sessionId,
                    createdAt: new Date(),
                },
            });
            return auditLog;
        }
        catch (error) {
            this.logger.error(`Failed to create audit log: ${error}`);
            throw error;
        }
    }
    async getAuditLogs(customerId, options) {
        const where = {};
        if (customerId) {
            where.customerId = customerId;
        }
        if (options?.action) {
            where.action = options.action;
        }
        if (options?.entity) {
            where.entity = options.entity;
        }
        if (options?.customerId) {
            where.customerId = options.customerId;
        }
        if (options?.startDate || options?.endDate) {
            where.createdAt = {};
            if (options.startDate) {
                where.createdAt.gte = options.startDate;
            }
            if (options.endDate) {
                where.createdAt.lte = options.endDate;
            }
        }
        return this.prisma.auditLog.findMany({
            where,
            orderBy: { createdAt: 'desc' },
            take: options?.limit || 100,
            skip: options?.offset || 0,
            include: {
                customer: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        });
    }
    async getAuditLogsByUser(customerId, options) {
        return this.getAuditLogs(customerId, options);
    }
    async getAuditLogStatistics(options) {
        const where = {};
        if (options?.customerId) {
            where.customerId = options.customerId;
        }
        if (options?.startDate || options?.endDate) {
            where.createdAt = {};
            if (options.startDate) {
                where.createdAt.gte = options.startDate;
            }
            if (options.endDate) {
                where.createdAt.lte = options.endDate;
            }
        }
        const totalLogs = await this.prisma.auditLog.count({ where });
        const actionGroups = await this.prisma.auditLog.groupBy({
            by: ['action'],
            where,
            _count: { action: true },
        });
        const actionBreakdown = {};
        actionGroups.forEach(group => {
            actionBreakdown[group.action] = group._count.action;
        });
        const entityGroups = await this.prisma.auditLog.groupBy({
            by: ['entity'],
            where,
            _count: { entity: true },
        });
        const entityBreakdown = {};
        entityGroups.forEach(group => {
            entityBreakdown[group.entity] = group._count.entity;
        });
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const recentActions = await this.prisma.auditLog.count({
            where: {
                ...where,
                createdAt: {
                    gte: yesterday,
                },
            },
        });
        return {
            totalLogs,
            actionBreakdown,
            entityBreakdown,
            recentActions,
        };
    }
    async logCustomerAction(customerId, action, entity, entityId, oldValues, newValues, user, description, metadata) {
        return this.logAction({
            customerId,
            action,
            entity,
            entityId,
            oldValues,
            newValues,
            description,
            metadata: {
                ...metadata,
                userId: user?.id,
                userEmail: user?.email,
            },
        });
    }
};
exports.CustomersAuditService = CustomersAuditService;
exports.CustomersAuditService = CustomersAuditService = CustomersAuditService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CustomersAuditService);
//# sourceMappingURL=customers-audit.service.js.map