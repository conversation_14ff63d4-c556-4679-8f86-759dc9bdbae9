"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GraphQLAuthGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphQLAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const core_1 = require("@nestjs/core");
const auth_service_1 = require("../../auth-shared/auth.service");
const public_decorator_1 = require("../../auth-shared/decorators/public.decorator");
let GraphQLAuthGuard = GraphQLAuthGuard_1 = class GraphQLAuthGuard {
    authService;
    reflector;
    logger = new common_1.Logger(GraphQLAuthGuard_1.name);
    constructor(authService, reflector) {
        this.authService = authService;
        this.reflector = reflector;
    }
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride(public_decorator_1.IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            this.logger.log('GraphQL route is public, skipping authentication');
            return true;
        }
        const ctx = graphql_1.GqlExecutionContext.create(context);
        const request = ctx.getContext().req;
        if (!request) {
            this.logger.error('No request object found in GraphQL context');
            return false;
        }
        try {
            const cookies = request.cookies || {};
            this.logger.log('GraphQL Auth Guard: Authenticating request');
            const user = await this.authService.authenticateFromCookies(cookies);
            request.user = user;
            this.logger.log(`GraphQL authentication successful for user: ${user.email}`);
            return true;
        }
        catch (error) {
            this.logger.error('GraphQL authentication failed:', error.message);
            return false;
        }
    }
};
exports.GraphQLAuthGuard = GraphQLAuthGuard;
exports.GraphQLAuthGuard = GraphQLAuthGuard = GraphQLAuthGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [auth_service_1.AuthSharedService,
        core_1.Reflector])
], GraphQLAuthGuard);
//# sourceMappingURL=graphql-auth.guard.js.map