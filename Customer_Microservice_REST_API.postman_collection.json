{"info": {"_postman_id": "customer-microservice-rest-api", "name": "Customer Microservice - REST API", "description": "Complete REST API collection for Customer Microservice with all endpoints and tests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3060", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}], "item": [{"name": "Health Checks", "item": [{"name": "Public Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has status ok', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('ok');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "Admin Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has admin access details', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.access).to.eql('admin');", "    pm.expect(jsonData.details).to.be.an('object');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/admin", "host": ["{{base_url}}"], "path": ["health", "admin"]}}}]}, {"name": "Authentication", "item": [{"name": "Auth Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Auth service is running', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('ok');", "    pm.expect(jsonData.service).to.eql('auth-shared');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/status", "host": ["{{base_url}}"], "path": ["auth", "status"]}}}, {"name": "Get Current User (Cookies)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User data is returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.be.a('string');", "    pm.expect(jsonData.email).to.be.a('string');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/me/cookies", "host": ["{{base_url}}"], "path": ["auth", "me", "cookies"]}}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Refresh successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}}, {"name": "Logout", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Logout successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}]}, {"name": "Customer Query", "item": [{"name": "Get All Customers", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has data array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.be.an('array');", "    pm.expect(jsonData.total).to.be.a('number');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/customers?limit=10&page=1", "host": ["{{base_url}}"], "path": ["customers"], "query": [{"key": "limit", "value": "10"}, {"key": "page", "value": "1"}]}}}, {"name": "Get Customer by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Customer data is returned', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.id).to.be.a('string');", "        pm.expect(jsonData.email).to.be.a('string');", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}"]}}}, {"name": "Get Customer by Email", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Customer data is returned', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.email).to.eql('<EMAIL>');", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/customers/email/<EMAIL>", "host": ["{{base_url}}"], "path": ["customers", "email", "<EMAIL>"]}}}]}, {"name": "Customer Management", "item": [{"name": "Create Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Customer created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.be.a('string');", "    pm.expect(jsonData.email).to.eql('<EMAIL>');", "    pm.globals.set('customer_id', jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nameOnCard\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"+1234567890\",\n  \"billingAddress\": \"123 Main St\",\n  \"country\": \"US\",\n  \"state\": \"CA\",\n  \"city\": \"San Francisco\",\n  \"zipCode\": \"94105\"\n}"}, "url": {"raw": "{{base_url}}/customers", "host": ["{{base_url}}"], "path": ["customers"]}}}, {"name": "Update Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.be.a('string');", "    pm.expect(jsonData.city).to.eql('Los Angeles');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"city\": \"Los Angeles\",\n  \"state\": \"CA\"\n}"}, "url": {"raw": "{{base_url}}/customers/{{customer_id}}", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}"]}}}, {"name": "Delete Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer deleted successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}"]}}}]}, {"name": "Customer Verification", "item": [{"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email verified successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.isEmailVerified).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}/verify-email", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}", "verify-email"]}}}, {"name": "Verify Phone", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Phone verified successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.isPhoneVerified).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}/verify-phone", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}", "verify-phone"]}}}, {"name": "Verify KYC", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('KYC verified successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.isKycVerified).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}/verify-kyc", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}", "verify-kyc"]}}}]}, {"name": "Customer Admin", "item": [{"name": "Suspend Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer suspended successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('SUSPENDED');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}/suspend", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}", "suspend"]}}}, {"name": "Activate Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer activated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('ACTIVE');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}/activate", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}", "activate"]}}}, {"name": "Block Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer blocked successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('BLOCKED');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/customers/{{customer_id}}/block", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}", "block"]}}}, {"name": "Admin Update Customer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer updated by admin successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.be.a('string');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\",\n  \"isEmailVerified\": true,\n  \"isPhoneVerified\": true,\n  \"notes\": \"Admin verified customer\"\n}"}, "url": {"raw": "{{base_url}}/customers/{{customer_id}}/admin-update", "host": ["{{base_url}}"], "path": ["customers", "{{customer_id}}", "admin-update"]}}}]}, {"name": "Transactions", "item": [{"name": "Initiate Transaction", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Transaction initiated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.transactionId).to.be.a('string');", "    pm.expect(jsonData.status).to.be.a('string');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100.00,\n  \"currency\": \"USD\",\n  \"description\": \"Test transaction\",\n  \"customerId\": \"{{customer_id}}\"\n}"}, "url": {"raw": "{{base_url}}/transactions/initiate", "host": ["{{base_url}}"], "path": ["transactions", "initiate"]}}}]}, {"name": "Webhooks", "item": [{"name": "Generic <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Webhook processed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('processed');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"test\",\n  \"data\": {\n    \"message\": \"Test webhook payload\"\n  }\n}"}, "url": {"raw": "{{base_url}}/webhooks", "host": ["{{base_url}}"], "path": ["webhooks"]}}}, {"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Elavon webhook processed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('processed');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reference_id\": \"test-ref-123\",\n  \"currency\": \"USD\",\n  \"amount\": 100.00,\n  \"provider_reference_id\": \"elavon-ref-456\",\n  \"elavon_token\": \"test-elavon-token\",\n  \"transaction_id\": \"txn-789\",\n  \"customer_name\": \"<PERSON>\",\n  \"customer_id\": \"cust-123\",\n  \"card_type\": \"VISA\",\n  \"status\": \"SUCCESS\",\n  \"response_code\": \"00\"\n}"}, "url": {"raw": "{{base_url}}/webhooks/elavon", "host": ["{{base_url}}"], "path": ["webhooks", "elavon"]}}}, {"name": "TSEP Webhook", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('TSEP webhook processed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('processed');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reference_id\": \"test-ref-123\",\n  \"currency\": \"USD\",\n  \"amount\": 100.00,\n  \"provider_reference_id\": \"tsep-ref-456\",\n  \"tsep_token\": \"test-tsep-token\",\n  \"transaction_id\": \"txn-789\",\n  \"customer_name\": \"<PERSON>\",\n  \"customer_id\": \"cust-123\",\n  \"card_type\": \"MASTERCARD\",\n  \"status\": \"SUCCESS\",\n  \"response_code\": \"00\"\n}"}, "url": {"raw": "{{base_url}}/webhooks/tsep", "host": ["{{base_url}}"], "path": ["webhooks", "tsep"]}}}, {"name": "Webhook Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Webhook service is healthy', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('ok');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/webhooks/health", "host": ["{{base_url}}"], "path": ["webhooks", "health"]}}}]}]}