"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PaymentTokensService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentTokensService = void 0;
const common_1 = require("@nestjs/common");
const crypto_1 = require("crypto");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let PaymentTokensService = PaymentTokensService_1 = class PaymentTokensService {
    prisma;
    logger = new common_1.Logger(PaymentTokensService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async processNearPayWebhook(webhookData, clientIp) {
        this.logger.log(`Processing NearPay webhook for transaction: ${webhookData.transactionId}`);
        try {
            const customer = await this.findCustomerFromWebhook(webhookData);
            if (!customer) {
                throw new common_1.NotFoundException('Customer not found for payment token creation');
            }
            if (webhookData.status !== 'APPROVED') {
                this.logger.warn(`Skipping token creation for non-approved transaction: ${webhookData.transactionId}`);
                throw new common_1.BadRequestException('Only approved transactions can create payment tokens');
            }
            const tokenData = `${customer.id}:${webhookData.card.last4}:${webhookData.card.brand}:${webhookData.merchant.merchantId}`;
            const tokenHash = this.createTokenHash(tokenData);
            let paymentToken = await this.prisma.paymentToken.findUnique({
                where: { tokenHash },
                include: { customer: true },
            });
            if (paymentToken) {
                paymentToken = await this.prisma.paymentToken.update({
                    where: { id: paymentToken.id },
                    data: {
                        lastUsedAt: new Date(),
                        usageCount: { increment: 1 },
                        rawWebhookData: {
                            transactionId: webhookData.transactionId,
                            amount: webhookData.amount,
                            currency: webhookData.currency,
                            merchantId: webhookData.merchant.merchantId,
                            terminalId: webhookData.merchant.terminalId,
                            cardType: webhookData.card.cardType,
                            last4: webhookData.card.last4,
                            authCode: webhookData.authCode,
                            timestamp: webhookData.timestamp,
                        },
                    },
                    include: { customer: true },
                });
                this.logger.log(`Updated existing payment token: ${paymentToken.id}`);
            }
            else {
                paymentToken = await this.prisma.paymentToken.create({
                    data: {
                        customerId: customer.id,
                        tokenHash,
                        externalTokenId: webhookData.transactionId,
                        paymentProvider: client_1.PaymentProvider.NEARPAY,
                        tokenType: client_1.PaymentTokenType.TAP_TO_PAY,
                        status: client_1.PaymentTokenStatus.ACTIVE,
                        maskedInfo: `**** **** **** ${webhookData.card.last4}`,
                        paymentBrand: webhookData.card.brand,
                        createdByIp: clientIp,
                        lastUsedAt: new Date(),
                        usageCount: 1,
                        providerMetadata: {
                            cardType: webhookData.card.cardType,
                            merchantName: webhookData.merchant.merchantName,
                            terminalId: webhookData.merchant.terminalId,
                        },
                        rawWebhookData: {
                            transactionId: webhookData.transactionId,
                            amount: webhookData.amount,
                            currency: webhookData.currency,
                            merchantId: webhookData.merchant.merchantId,
                            terminalId: webhookData.merchant.terminalId,
                            cardType: webhookData.card.cardType,
                            last4: webhookData.card.last4,
                            authCode: webhookData.authCode,
                            timestamp: webhookData.timestamp,
                        },
                    },
                    include: { customer: true },
                });
                this.logger.log(`Created new payment token: ${paymentToken?.id} for customer: ${customer.id}`);
            }
            return this.mapToResponseDto(paymentToken);
        }
        catch (error) {
            this.logger.error(`Failed to process NearPay webhook: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getCustomerPaymentTokens(customerId) {
        const tokens = await this.prisma.paymentToken.findMany({
            where: {
                customerId,
                deletedAt: null,
                status: client_1.PaymentTokenStatus.ACTIVE,
            },
            orderBy: { lastUsedAt: 'desc' },
            include: { customer: true },
        });
        return tokens.map(token => this.mapToResponseDto(token));
    }
    async revokePaymentToken(tokenId, customerId) {
        const token = await this.prisma.paymentToken.findFirst({
            where: { id: tokenId, customerId },
        });
        if (!token) {
            throw new common_1.NotFoundException('Payment token not found');
        }
        await this.prisma.paymentToken.update({
            where: { id: tokenId },
            data: { status: client_1.PaymentTokenStatus.REVOKED },
        });
        this.logger.log(`Revoked payment token: ${tokenId} for customer: ${customerId}`);
    }
    async findCustomerFromWebhook(webhookData) {
        let customer = null;
        if (webhookData.customerId) {
            customer = await this.prisma.customer.findUnique({
                where: { id: webhookData.customerId },
            });
        }
        if (!customer && webhookData.customerEmail) {
            customer = await this.prisma.customer.findUnique({
                where: { email: webhookData.customerEmail },
            });
        }
        if (!customer && webhookData.customerPhone) {
            customer = await this.prisma.customer.findFirst({
                where: { phone: webhookData.customerPhone },
            });
        }
        return customer;
    }
    createTokenHash(data) {
        return (0, crypto_1.createHash)('sha256').update(data).digest('hex');
    }
    mapToResponseDto(token) {
        return {
            id: token.id,
            customerId: token.customerId,
            paymentProvider: token.paymentProvider,
            tokenType: token.tokenType,
            status: token.status,
            maskedInfo: token.maskedInfo,
            paymentBrand: token.paymentBrand,
            expiresAt: token.expiresAt,
            lastUsedAt: token.lastUsedAt,
            usageCount: token.usageCount,
            createdAt: token.createdAt,
        };
    }
};
exports.PaymentTokensService = PaymentTokensService;
exports.PaymentTokensService = PaymentTokensService = PaymentTokensService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PaymentTokensService);
//# sourceMappingURL=payment-tokens.service.js.map