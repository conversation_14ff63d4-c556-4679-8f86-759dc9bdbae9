"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TransactionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionService = void 0;
const common_1 = require("@nestjs/common");
let TransactionService = TransactionService_1 = class TransactionService {
    logger = new common_1.Logger(TransactionService_1.name);
    constructor() { }
    async initiateTransaction(user, transactionData) {
        try {
            this.logger.log(`Initiating transaction ${transactionData.transactionId} for user: ${user.email}`);
            if (!user.phone) {
                throw new common_1.BadRequestException('Phone number is required for transaction processing');
            }
            if (transactionData.amount <= 0) {
                throw new common_1.BadRequestException('Transaction amount must be greater than 0');
            }
            await this.sendOtp(user.phone, transactionData.transactionId);
            return {
                transactionId: transactionData.transactionId,
                status: 'otp_sent',
                message: `OTP sent to ${this.maskPhoneNumber(user.phone)}`,
                userDetails: {
                    id: user.id,
                    email: user.email,
                    password: user.password,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    phone: {
                        number: user.phone,
                        masked: this.maskPhoneNumber(user.phone)
                    },
                    country: user.country,
                    verifiedEmail: user.verifiedEmail,
                    verifiedPhone: user.verifiedPhone,
                    totpSecret: user.totpSecret,
                    role: user.role,
                    createdAt: user.createdAt,
                    partnerId: user.partnerId,
                    mfaEnabled: user.mfaEnabled,
                    active: user.active,
                    accountId: user.accountId,
                    isAdmin: user.isAdmin || false,
                    permissions: user.permissions || [],
                },
            };
        }
        catch (error) {
            this.logger.error(`Failed to initiate transaction: ${error.message}`);
            throw new common_1.BadRequestException(`Transaction initiation failed: ${error.message}`);
        }
    }
    async verifyOtpAndCompleteTransaction(user, otpData) {
        try {
            this.logger.log(`Verifying OTP for transaction: ${otpData.transactionId}`);
            if (user.phone !== otpData.phoneNumber) {
                throw new common_1.UnauthorizedException('Phone number does not match user account');
            }
            const isOtpValid = await this.verifyOtp(otpData.phoneNumber, otpData.otp, otpData.transactionId);
            if (!isOtpValid) {
                return {
                    transactionId: otpData.transactionId,
                    status: 'failed',
                    message: 'Invalid OTP. Please try again.',
                };
            }
            const paymentDetails = await this.generatePaymentDetails(user, otpData.transactionId);
            return {
                transactionId: otpData.transactionId,
                status: 'completed',
                message: 'Transaction completed successfully',
                userDetails: {
                    id: user.id,
                    email: user.email,
                    password: user.password,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    phone: {
                        number: user.phone,
                        masked: this.maskPhoneNumber(user.phone)
                    },
                    country: user.country,
                    verifiedEmail: user.verifiedEmail,
                    verifiedPhone: user.verifiedPhone,
                    totpSecret: user.totpSecret,
                    role: user.role,
                    createdAt: user.createdAt,
                    partnerId: user.partnerId,
                    mfaEnabled: user.mfaEnabled,
                    active: user.active,
                    accountId: user.accountId,
                    isAdmin: user.isAdmin || false,
                    permissions: user.permissions || [],
                },
                paymentDetails,
            };
        }
        catch (error) {
            this.logger.error(`Failed to verify OTP: ${error.message}`);
            throw new common_1.BadRequestException(`OTP verification failed: ${error.message}`);
        }
    }
    async sendOtp(phoneNumber, transactionId) {
        try {
            this.logger.log(`Sending OTP to ${this.maskPhoneNumber(phoneNumber)} for transaction: ${transactionId}`);
            await new Promise(resolve => setTimeout(resolve, 100));
            this.logger.log(`OTP sent successfully to ${this.maskPhoneNumber(phoneNumber)}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to send OTP: ${error.message}`);
            throw error;
        }
    }
    async verifyOtp(phoneNumber, otp, transactionId) {
        try {
            this.logger.log(`Verifying OTP for ${this.maskPhoneNumber(phoneNumber)}, transaction: ${transactionId}`);
            const isValid = otp === '123456';
            this.logger.log(`OTP verification result: ${isValid ? 'SUCCESS' : 'FAILED'}`);
            return isValid;
        }
        catch (error) {
            this.logger.error(`Failed to verify OTP: ${error.message}`);
            return false;
        }
    }
    async generatePaymentDetails(user, transactionId) {
        try {
            this.logger.log(`Generating payment details for user: ${user.email}, transaction: ${transactionId}`);
            const paymentDetails = {
                routingNumber: '*********',
                cvv: '123',
                accountNumber: '****1234',
                expiryDate: '12/25',
            };
            this.logger.log(`Payment details generated successfully for transaction: ${transactionId}`);
            return paymentDetails;
        }
        catch (error) {
            this.logger.error(`Failed to generate payment details: ${error.message}`);
            throw error;
        }
    }
    maskPhoneNumber(phoneNumber) {
        if (!phoneNumber || phoneNumber.length < 4) {
            return '***';
        }
        const lastFour = phoneNumber.slice(-4);
        const masked = phoneNumber.slice(0, -4).replace(/\d/g, '*');
        return `${masked}${lastFour}`;
    }
};
exports.TransactionService = TransactionService;
exports.TransactionService = TransactionService = TransactionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], TransactionService);
//# sourceMappingURL=transaction.service.js.map