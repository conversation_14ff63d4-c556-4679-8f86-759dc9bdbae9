{"version": 3, "file": "customers-access-control.service.js", "sourceRoot": "", "sources": ["../../../src/modules/customers-access-control/customers-access-control.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAwE;AAcjE,IAAM,6BAA6B,qCAAnC,MAAM,6BAA6B;IACvB,MAAM,GAAG,IAAI,eAAM,CAAC,+BAA6B,CAAC,IAAI,CAAC,CAAC;IAKzE,eAAe,CAAC,IAAc,EAAE,UAAkB;QAChD,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1E,CAAC;IAKD,gBAAgB,CAAC,IAAc;QAC7B,OAAO,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC;IAC/B,CAAC;IAKD,mBAAmB,CACjB,IAAc,EACd,QAA+B,EAC/B,UAAgC,EAAE;QAGlC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,OAAO,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACxF,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,OAAO,CAAC,eAAe,IAAI,QAAQ,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAGjD,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAGjD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,yBAAyB,CACvB,IAAc,EACd,QAAmC,EAAE,EACrC,UAAgC,EAAE;QAGlC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YAEzB,OAAO,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC;QAC3C,CAAC;QAGD,IAAI,OAAO,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACxF,OAAO,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC;QAC3C,CAAC;QAED,MAAM,aAAa,GAAgC,EAAE,CAAC;QAGtD,IAAI,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACvC,aAAa,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9C,CAAC;QAGD,IAAI,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QAInD,CAAC;QAGD,IAAI,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QAInD,CAAC;QAGD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC;QAC3C,CAAC;QAGD,OAAO;YACL,GAAG,KAAK;YACR,EAAE,EAAE,aAAa;SAClB,CAAC;IACJ,CAAC;IAKD,sBAAsB,CACpB,IAAc,EACd,QAA+B,EAC/B,MAAc,EACd,UAAgC,EAAE;QAElC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,0BAA0B,IAAI,CAAC,EAAE,OAAO,MAAM,aAAa,QAAQ,CAAC,EAAE,EAAE,CACzE,CAAC;YACF,MAAM,IAAI,2BAAkB,CAAC,oBAAoB,MAAM,gBAAgB,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAKD,kBAAkB,CAAC,IAAc,EAAE,UAAkB,EAAE,MAAc;QACnE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,8BAA8B,IAAI,CAAC,EAAE,OAAO,MAAM,cAAc,UAAU,GAAG,CAC9E,CAAC;YACF,MAAM,IAAI,2BAAkB,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKD,mBAAmB,CAAC,IAAc,EAAE,MAAc;QAChD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,CAAC,EAAE,OAAO,MAAM,EAAE,CAAC,CAAC;YACzE,MAAM,IAAI,2BAAkB,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKD,iBAAiB,CAAC,SAAgD;QAChE,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO;oBACL,iBAAiB,EAAE,gBAAgB;oBACnC,eAAe,EAAE,IAAI;oBACrB,kBAAkB,EAAE,IAAI;oBACxB,kBAAkB,EAAE,IAAI;iBACzB,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,iBAAiB,EAAE,iBAAiB;oBACpC,eAAe,EAAE,KAAK;oBACtB,kBAAkB,EAAE,IAAI;oBACxB,kBAAkB,EAAE,IAAI;iBACzB,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO;oBACL,iBAAiB,EAAE,kBAAkB;oBACrC,eAAe,EAAE,KAAK;oBACtB,kBAAkB,EAAE,KAAK;oBACzB,kBAAkB,EAAE,KAAK;iBAC1B,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,YAAY,EAAE,IAAI;iBACnB,CAAC;YACJ;gBACE,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAKD,kBAAkB,CAChB,IAAc,EACd,QAA+B,EAC/B,UAAgC,EAAE;QAGlC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAGD,MAAM,gBAAgB,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;QAGzC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,yBAAyB,CAAC,EAAE,CAAC;YAC3D,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC;YAC9B,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAAE,CAAC;YAC1D,gBAAgB,CAAC,QAAQ,GAAG,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,yBAAyB,CAAC,EAAE,CAAC;YAC3D,gBAAgB,CAAC,SAAS,GAAG,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,2BAA2B,CAAC,EAAE,CAAC;YAC7D,gBAAgB,CAAC,WAAW,GAAG,EAAE,CAAC;QACpC,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAKD,wBAAwB,CAAC,IAAc,EAAE,SAA8B;QACrE,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAG,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,uBAAuB,CAAC;QAC9F,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAChD,CAAC;IAKD,2BAA2B,CAAC,IAAc,EAAE,SAA8B;QACxE,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,QAAQ,SAAS,2BAA2B,IAAI,CAAC,EAAE,EAAE,CACtD,CAAC;YACF,MAAM,IAAI,2BAAkB,CAAC,8BAA8B,SAAS,aAAa,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;CACF,CAAA;AAhQY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;GACA,6BAA6B,CAgQzC"}