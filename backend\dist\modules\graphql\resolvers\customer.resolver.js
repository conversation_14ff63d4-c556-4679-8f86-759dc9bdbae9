"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CustomerResolver_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const customer_types_1 = require("../types/customer.types");
const customer_inputs_1 = require("../inputs/customer.inputs");
const customers_query_service_1 = require("../../customers-query/customers-query.service");
const customers_management_service_1 = require("../../customers-management/customers-management.service");
const customers_verification_service_1 = require("../../customers-verification/customers-verification.service");
const customers_admin_service_1 = require("../../customers-admin/customers-admin.service");
const graphql_auth_guard_1 = require("../guards/graphql-auth.guard");
const public_decorator_1 = require("../../auth-shared/decorators/public.decorator");
let CustomerResolver = CustomerResolver_1 = class CustomerResolver {
    queryService;
    managementService;
    verificationService;
    adminService;
    logger = new common_1.Logger(CustomerResolver_1.name);
    constructor(queryService, managementService, verificationService, adminService) {
        this.queryService = queryService;
        this.managementService = managementService;
        this.verificationService = verificationService;
        this.adminService = adminService;
    }
    mapToGraphQLCustomer(customer) {
        const graphqlCustomer = new customer_types_1.Customer();
        Object.assign(graphqlCustomer, customer);
        graphqlCustomer.externalId = customer.externalId || undefined;
        return graphqlCustomer;
    }
    mapToGraphQLCustomerArray(customers) {
        return customers.map(customer => this.mapToGraphQLCustomer(customer));
    }
    async getCustomers(page, limit, filter) {
        this.logger.log(`Getting customers with page: ${page}, limit: ${limit}, filter: ${filter}`);
        const skip = ((page || 1) - 1) * (limit || 10);
        const take = limit || 10;
        const customerFilter = filter ? {
            search: filter
        } : {};
        const paginationOptions = {
            skip,
            take,
            orderBy: { createdAt: 'desc' }
        };
        const customers = await this.queryService.findMany(customerFilter, paginationOptions);
        const total = await this.queryService.count(customerFilter);
        return {
            data: this.mapToGraphQLCustomerArray(customers),
            total,
            page: page || 1,
            limit: limit || 10
        };
    }
    async getCustomer(id) {
        this.logger.log(`Getting customer ${id}`);
        const customer = await this.queryService.findById(id);
        return customer ? this.mapToGraphQLCustomer(customer) : null;
    }
    async getCustomerByEmail(email) {
        this.logger.log(`Getting customer by email ${email}`);
        const customer = await this.queryService.findByEmail(email);
        return customer ? this.mapToGraphQLCustomer(customer) : null;
    }
    async searchCustomers(filter, pagination) {
        this.logger.log(`Advanced search`);
        const orderBy = { createdAt: 'desc' };
        const paginationOptions = {
            skip: pagination?.skip,
            take: pagination?.take,
            orderBy
        };
        const customers = await this.queryService.findMany(filter, paginationOptions);
        return this.mapToGraphQLCustomerArray(customers);
    }
    async createCustomer(input) {
        this.logger.log(`Creating customer`);
        const customerData = {
            ...input,
            dateOfBirth: input.dateOfBirth ? new Date(input.dateOfBirth) : undefined,
        };
        const customer = await this.managementService.create(customerData);
        return this.mapToGraphQLCustomer(customer);
    }
    async updateCustomer(input) {
        this.logger.log(`Updating customer ${input.id}`);
        const { id, ...updateData } = input;
        const customerData = {
            ...updateData,
            dateOfBirth: updateData.dateOfBirth ? new Date(updateData.dateOfBirth) : undefined,
        };
        const customer = await this.managementService.update(id, customerData);
        return this.mapToGraphQLCustomer(customer);
    }
    async deleteCustomer(id) {
        this.logger.log(`Deleting customer ${id}`);
        await this.managementService.delete(id);
        return true;
    }
    async verifyCustomerEmail(id) {
        this.logger.log(`Verifying email for customer ${id}`);
        const customer = await this.verificationService.verifyEmail(id);
        return this.mapToGraphQLCustomer(customer);
    }
    async verifyCustomerPhone(id) {
        this.logger.log(`Verifying phone for customer ${id}`);
        const customer = await this.verificationService.verifyPhone(id);
        return this.mapToGraphQLCustomer(customer);
    }
    async getCustomersAdmin(filter, pagination) {
        this.logger.log(`Admin getting customers`);
        const orderBy = pagination?.take ? {
            createdAt: 'desc'
        } : { createdAt: 'desc' };
        const customers = await this.queryService.findMany(filter, { ...pagination, orderBy });
        return this.mapToGraphQLCustomerArray(customers);
    }
    async updateCustomerStatus(id, status) {
        this.logger.log(`Admin updating customer ${id} status to ${status}`);
        const customer = await this.adminService.adminUpdateCustomer(id, { status: status });
        return this.mapToGraphQLCustomer(customer);
    }
};
exports.CustomerResolver = CustomerResolver;
__decorate([
    (0, graphql_1.Query)(() => customer_types_1.PaginatedCustomers, { name: 'customers' }),
    (0, public_decorator_1.Public)(),
    __param(0, (0, graphql_1.Args)('page', { type: () => graphql_1.Int, nullable: true, defaultValue: 1 })),
    __param(1, (0, graphql_1.Args)('limit', { type: () => graphql_1.Int, nullable: true, defaultValue: 10 })),
    __param(2, (0, graphql_1.Args)('filter', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "getCustomers", null);
__decorate([
    (0, graphql_1.Query)(() => customer_types_1.Customer, { name: 'customer', nullable: true }),
    (0, public_decorator_1.Public)(),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "getCustomer", null);
__decorate([
    (0, graphql_1.Query)(() => customer_types_1.Customer, { name: 'customerByEmail', nullable: true }),
    __param(0, (0, graphql_1.Args)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "getCustomerByEmail", null);
__decorate([
    (0, graphql_1.Query)(() => [customer_types_1.Customer], { name: 'searchCustomers' }),
    __param(0, (0, graphql_1.Args)('filter', { nullable: true })),
    __param(1, (0, graphql_1.Args)('pagination', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [customer_inputs_1.CustomerFilterInput,
        customer_inputs_1.PaginationInput]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "searchCustomers", null);
__decorate([
    (0, graphql_1.Mutation)(() => customer_types_1.Customer),
    __param(0, (0, graphql_1.Args)('input')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [customer_inputs_1.CreateCustomerInput]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "createCustomer", null);
__decorate([
    (0, graphql_1.Mutation)(() => customer_types_1.Customer),
    __param(0, (0, graphql_1.Args)('input')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [customer_inputs_1.UpdateCustomerInput]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "updateCustomer", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "deleteCustomer", null);
__decorate([
    (0, graphql_1.Mutation)(() => customer_types_1.Customer),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "verifyCustomerEmail", null);
__decorate([
    (0, graphql_1.Mutation)(() => customer_types_1.Customer),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "verifyCustomerPhone", null);
__decorate([
    (0, graphql_1.Query)(() => [customer_types_1.Customer], { name: 'customersAdmin' }),
    __param(0, (0, graphql_1.Args)('filter', { nullable: true })),
    __param(1, (0, graphql_1.Args)('pagination', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [customer_inputs_1.CustomerFilterInput,
        customer_inputs_1.PaginationInput]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "getCustomersAdmin", null);
__decorate([
    (0, graphql_1.Mutation)(() => customer_types_1.Customer),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __param(1, (0, graphql_1.Args)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomerResolver.prototype, "updateCustomerStatus", null);
exports.CustomerResolver = CustomerResolver = CustomerResolver_1 = __decorate([
    (0, graphql_1.Resolver)(() => customer_types_1.Customer),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GraphQLAuthGuard),
    __metadata("design:paramtypes", [customers_query_service_1.CustomersQueryService,
        customers_management_service_1.CustomersManagementService,
        customers_verification_service_1.CustomersVerificationService,
        customers_admin_service_1.CustomersAdminService])
], CustomerResolver);
//# sourceMappingURL=customer.resolver.js.map